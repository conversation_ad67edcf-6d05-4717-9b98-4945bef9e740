{"name": "mvp", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build --turbopack", "start": "next start", "lint": "eslint"}, "dependencies": {"@radix-ui/react-dialog": "^1.1.15", "@radix-ui/react-dropdown-menu": "^2.1.16", "@radix-ui/react-select": "^2.2.6", "@radix-ui/react-tabs": "^1.1.13", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "firebase": "^12.2.1", "googleapis": "^159.0.0", "lucide-react": "^0.542.0", "next": "15.5.2", "react": "19.1.0", "react-dom": "19.1.0", "tailwind-merge": "^3.3.1", "zod": "^4.1.5"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.5.2", "tailwindcss": "^4", "typescript": "^5"}}