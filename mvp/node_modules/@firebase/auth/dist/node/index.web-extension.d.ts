/**
 * @license
 * Copyright 2023 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
export * from './src';
import { indexedDBLocalPersistence } from './src/platform_browser/persistence/indexed_db';
import { TotpMultiFactorGenerator, TotpSecret } from './src/mfa/assertions/totp';
import { FirebaseApp } from '@firebase/app';
import { Auth } from './index.shared';
/**
 * Returns the Auth instance associated with the provided {@link @firebase/app#FirebaseApp}.
 * If no instance exists, initializes an Auth instance with platform-specific default dependencies.
 *
 * @param app - The Firebase App.
 *
 * @public
 */
declare function getAuth(app?: FirebaseApp): Auth;
export { indexedDBLocalPersistence, TotpMultiFactorGenerator, TotpSecret, getAuth };
