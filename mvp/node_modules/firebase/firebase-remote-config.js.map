{"version": 3, "file": "firebase-remote-config.js", "sources": ["../util/src/environment.ts", "../util/src/errors.ts", "../util/src/obj.ts", "../util/src/exponential_backoff.ts", "../util/src/compat.ts", "../component/src/component.ts", "../logger/src/logger.ts", "../../node_modules/idb/build/wrap-idb-value.js", "../../node_modules/idb/build/index.js", "../installations/src/util/constants.ts", "../installations/src/util/errors.ts", "../installations/src/functions/common.ts", "../installations/src/util/sleep.ts", "../installations/src/helpers/generate-fid.ts", "../installations/src/helpers/buffer-to-base64-url-safe.ts", "../installations/src/util/get-key.ts", "../installations/src/helpers/fid-changed.ts", "../installations/src/helpers/idb-manager.ts", "../installations/src/helpers/get-installation-entry.ts", "../installations/src/functions/create-installation-request.ts", "../installations/src/functions/generate-auth-token-request.ts", "../installations/src/helpers/refresh-auth-token.ts", "../installations/src/api/get-token.ts", "../installations/src/helpers/extract-app-config.ts", "../installations/src/functions/config.ts", "../installations/src/api/get-id.ts", "../installations/src/index.ts", "../remote-config/src/client/remote_config_fetch_client.ts", "../remote-config/src/constants.ts", "../remote-config/src/errors.ts", "../remote-config/src/value.ts", "../remote-config/src/api.ts", "../remote-config/src/client/caching_client.ts", "../remote-config/src/language.ts", "../remote-config/src/client/rest_client.ts", "../remote-config/src/client/retrying_client.ts", "../remote-config/src/remote_config.ts", "../remote-config/src/storage/storage.ts", "../remote-config/src/storage/storage_cache.ts", "../remote-config/src/api2.ts", "../remote-config/src/register.ts", "../remote-config/src/index.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { CONSTANTS } from './constants';\nimport { getDefaults } from './defaults';\n\n/**\n * Type placeholder for `WorkerGlobalScope` from `webworker`\n */\ndeclare class WorkerGlobalScope {}\n\n/**\n * Returns navigator.userAgent string or '' if it's not defined.\n * @return user agent string\n */\nexport function getUA(): string {\n  if (\n    typeof navigator !== 'undefined' &&\n    typeof navigator['userAgent'] === 'string'\n  ) {\n    return navigator['userAgent'];\n  } else {\n    return '';\n  }\n}\n\n/**\n * Detect Cordova / PhoneGap / Ionic frameworks on a mobile device.\n *\n * Deliberately does not rely on checking `file://` URLs (as this fails PhoneGap\n * in the Ripple emulator) nor Cordova `onDeviceReady`, which would normally\n * wait for a callback.\n */\nexport function isMobileCordova(): boolean {\n  return (\n    typeof window !== 'undefined' &&\n    // @ts-ignore Setting up an broadly applicable index signature for Window\n    // just to deal with this case would probably be a bad idea.\n    !!(window['cordova'] || window['phonegap'] || window['PhoneGap']) &&\n    /ios|iphone|ipod|ipad|android|blackberry|iemobile/i.test(getUA())\n  );\n}\n\n/**\n * Detect Node.js.\n *\n * @return true if Node.js environment is detected or specified.\n */\n// Node detection logic from: https://github.com/iliakan/detect-node/\nexport function isNode(): boolean {\n  const forceEnvironment = getDefaults()?.forceEnvironment;\n  if (forceEnvironment === 'node') {\n    return true;\n  } else if (forceEnvironment === 'browser') {\n    return false;\n  }\n\n  try {\n    return (\n      Object.prototype.toString.call(global.process) === '[object process]'\n    );\n  } catch (e) {\n    return false;\n  }\n}\n\n/**\n * Detect Browser Environment.\n * Note: This will return true for certain test frameworks that are incompletely\n * mimicking a browser, and should not lead to assuming all browser APIs are\n * available.\n */\nexport function isBrowser(): boolean {\n  return typeof window !== 'undefined' || isWebWorker();\n}\n\n/**\n * Detect Web Worker context.\n */\nexport function isWebWorker(): boolean {\n  return (\n    typeof WorkerGlobalScope !== 'undefined' &&\n    typeof self !== 'undefined' &&\n    self instanceof WorkerGlobalScope\n  );\n}\n\n/**\n * Detect Cloudflare Worker context.\n */\nexport function isCloudflareWorker(): boolean {\n  return (\n    typeof navigator !== 'undefined' &&\n    navigator.userAgent === 'Cloudflare-Workers'\n  );\n}\n\n/**\n * Detect browser extensions (Chrome and Firefox at least).\n */\ninterface BrowserRuntime {\n  id?: unknown;\n}\ndeclare const chrome: { runtime?: BrowserRuntime };\ndeclare const browser: { runtime?: BrowserRuntime };\nexport function isBrowserExtension(): boolean {\n  const runtime =\n    typeof chrome === 'object'\n      ? chrome.runtime\n      : typeof browser === 'object'\n      ? browser.runtime\n      : undefined;\n  return typeof runtime === 'object' && runtime.id !== undefined;\n}\n\n/**\n * Detect React Native.\n *\n * @return true if ReactNative environment is detected.\n */\nexport function isReactNative(): boolean {\n  return (\n    typeof navigator === 'object' && navigator['product'] === 'ReactNative'\n  );\n}\n\n/** Detects Electron apps. */\nexport function isElectron(): boolean {\n  return getUA().indexOf('Electron/') >= 0;\n}\n\n/** Detects Internet Explorer. */\nexport function isIE(): boolean {\n  const ua = getUA();\n  return ua.indexOf('MSIE ') >= 0 || ua.indexOf('Trident/') >= 0;\n}\n\n/** Detects Universal Windows Platform apps. */\nexport function isUWP(): boolean {\n  return getUA().indexOf('MSAppHost/') >= 0;\n}\n\n/**\n * Detect whether the current SDK build is the Node version.\n *\n * @return true if it's the Node SDK build.\n */\nexport function isNodeSdk(): boolean {\n  return CONSTANTS.NODE_CLIENT === true || CONSTANTS.NODE_ADMIN === true;\n}\n\n/** Returns true if we are running in Safari. */\nexport function isSafari(): boolean {\n  return (\n    !isNode() &&\n    !!navigator.userAgent &&\n    navigator.userAgent.includes('Safari') &&\n    !navigator.userAgent.includes('Chrome')\n  );\n}\n\n/** Returns true if we are running in Safari or WebKit */\nexport function isSafariOrWebkit(): boolean {\n  return (\n    !isNode() &&\n    !!navigator.userAgent &&\n    (navigator.userAgent.includes('Safari') ||\n      navigator.userAgent.includes('WebKit')) &&\n    !navigator.userAgent.includes('Chrome')\n  );\n}\n\n/**\n * This method checks if indexedDB is supported by current browser/service worker context\n * @return true if indexedDB is supported by current browser/service worker context\n */\nexport function isIndexedDBAvailable(): boolean {\n  try {\n    return typeof indexedDB === 'object';\n  } catch (e) {\n    return false;\n  }\n}\n\n/**\n * This method validates browser/sw context for indexedDB by opening a dummy indexedDB database and reject\n * if errors occur during the database open operation.\n *\n * @throws exception if current browser/sw context can't run idb.open (ex: Safari iframe, Firefox\n * private browsing)\n */\nexport function validateIndexedDBOpenable(): Promise<boolean> {\n  return new Promise((resolve, reject) => {\n    try {\n      let preExist: boolean = true;\n      const DB_CHECK_NAME =\n        'validate-browser-context-for-indexeddb-analytics-module';\n      const request = self.indexedDB.open(DB_CHECK_NAME);\n      request.onsuccess = () => {\n        request.result.close();\n        // delete database only when it doesn't pre-exist\n        if (!preExist) {\n          self.indexedDB.deleteDatabase(DB_CHECK_NAME);\n        }\n        resolve(true);\n      };\n      request.onupgradeneeded = () => {\n        preExist = false;\n      };\n\n      request.onerror = () => {\n        reject(request.error?.message || '');\n      };\n    } catch (error) {\n      reject(error);\n    }\n  });\n}\n\n/**\n *\n * This method checks whether cookie is enabled within current browser\n * @return true if cookie is enabled within current browser\n */\nexport function areCookiesEnabled(): boolean {\n  if (typeof navigator === 'undefined' || !navigator.cookieEnabled) {\n    return false;\n  }\n  return true;\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * @fileoverview Standardized Firebase Error.\n *\n * Usage:\n *\n *   // TypeScript string literals for type-safe codes\n *   type Err =\n *     'unknown' |\n *     'object-not-found'\n *     ;\n *\n *   // Closure enum for type-safe error codes\n *   // at-enum {string}\n *   var Err = {\n *     UNKNOWN: 'unknown',\n *     OBJECT_NOT_FOUND: 'object-not-found',\n *   }\n *\n *   let errors: Map<Err, string> = {\n *     'generic-error': \"Unknown error\",\n *     'file-not-found': \"Could not find file: {$file}\",\n *   };\n *\n *   // Type-safe function - must pass a valid error code as param.\n *   let error = new ErrorFactory<Err>('service', 'Service', errors);\n *\n *   ...\n *   throw error.create(Err.GENERIC);\n *   ...\n *   throw error.create(Err.FILE_NOT_FOUND, {'file': fileName});\n *   ...\n *   // Service: Could not file file: foo.txt (service/file-not-found).\n *\n *   catch (e) {\n *     assert(e.message === \"Could not find file: foo.txt.\");\n *     if ((e as FirebaseError)?.code === 'service/file-not-found') {\n *       console.log(\"Could not read file: \" + e['file']);\n *     }\n *   }\n */\n\nexport type ErrorMap<ErrorCode extends string> = {\n  readonly [K in ErrorCode]: string;\n};\n\nconst ERROR_NAME = 'FirebaseError';\n\nexport interface StringLike {\n  toString(): string;\n}\n\nexport interface ErrorData {\n  [key: string]: unknown;\n}\n\n// Based on code from:\n// https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Error#Custom_Error_Types\nexport class FirebaseError extends Error {\n  /** The custom name for all FirebaseErrors. */\n  readonly name: string = ERROR_NAME;\n\n  constructor(\n    /** The error code for this error. */\n    readonly code: string,\n    message: string,\n    /** Custom data for this error. */\n    public customData?: Record<string, unknown>\n  ) {\n    super(message);\n\n    // Fix For ES5\n    // https://github.com/Microsoft/TypeScript-wiki/blob/master/Breaking-Changes.md#extending-built-ins-like-error-array-and-map-may-no-longer-work\n    // TODO(dlarocque): Replace this with `new.target`: https://www.typescriptlang.org/docs/handbook/release-notes/typescript-2-2.html#support-for-newtarget\n    //                   which we can now use since we no longer target ES5.\n    Object.setPrototypeOf(this, FirebaseError.prototype);\n\n    // Maintains proper stack trace for where our error was thrown.\n    // Only available on V8.\n    if (Error.captureStackTrace) {\n      Error.captureStackTrace(this, ErrorFactory.prototype.create);\n    }\n  }\n}\n\nexport class ErrorFactory<\n  ErrorCode extends string,\n  ErrorParams extends { readonly [K in ErrorCode]?: ErrorData } = {}\n> {\n  constructor(\n    private readonly service: string,\n    private readonly serviceName: string,\n    private readonly errors: ErrorMap<ErrorCode>\n  ) {}\n\n  create<K extends ErrorCode>(\n    code: K,\n    ...data: K extends keyof ErrorParams ? [ErrorParams[K]] : []\n  ): FirebaseError {\n    const customData = (data[0] as ErrorData) || {};\n    const fullCode = `${this.service}/${code}`;\n    const template = this.errors[code];\n\n    const message = template ? replaceTemplate(template, customData) : 'Error';\n    // Service Name: Error message (service/code).\n    const fullMessage = `${this.serviceName}: ${message} (${fullCode}).`;\n\n    const error = new FirebaseError(fullCode, fullMessage, customData);\n\n    return error;\n  }\n}\n\nfunction replaceTemplate(template: string, data: ErrorData): string {\n  return template.replace(PATTERN, (_, key) => {\n    const value = data[key];\n    return value != null ? String(value) : `<${key}?>`;\n  });\n}\n\nconst PATTERN = /\\{\\$([^}]+)}/g;\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport function contains<T extends object>(obj: T, key: string): boolean {\n  return Object.prototype.hasOwnProperty.call(obj, key);\n}\n\nexport function safeGet<T extends object, K extends keyof T>(\n  obj: T,\n  key: K\n): T[K] | undefined {\n  if (Object.prototype.hasOwnProperty.call(obj, key)) {\n    return obj[key];\n  } else {\n    return undefined;\n  }\n}\n\nexport function isEmpty(obj: object): obj is {} {\n  for (const key in obj) {\n    if (Object.prototype.hasOwnProperty.call(obj, key)) {\n      return false;\n    }\n  }\n  return true;\n}\n\nexport function map<K extends string, V, U>(\n  obj: { [key in K]: V },\n  fn: (value: V, key: K, obj: { [key in K]: V }) => U,\n  contextObj?: unknown\n): { [key in K]: U } {\n  const res: Partial<{ [key in K]: U }> = {};\n  for (const key in obj) {\n    if (Object.prototype.hasOwnProperty.call(obj, key)) {\n      res[key] = fn.call(contextObj, obj[key], key, obj);\n    }\n  }\n  return res as { [key in K]: U };\n}\n\n/**\n * Deep equal two objects. Support Arrays and Objects.\n */\nexport function deepEqual(a: object, b: object): boolean {\n  if (a === b) {\n    return true;\n  }\n\n  const aKeys = Object.keys(a);\n  const bKeys = Object.keys(b);\n  for (const k of aKeys) {\n    if (!bKeys.includes(k)) {\n      return false;\n    }\n\n    const aProp = (a as Record<string, unknown>)[k];\n    const bProp = (b as Record<string, unknown>)[k];\n    if (isObject(aProp) && isObject(bProp)) {\n      if (!deepEqual(aProp, bProp)) {\n        return false;\n      }\n    } else if (aProp !== bProp) {\n      return false;\n    }\n  }\n\n  for (const k of bKeys) {\n    if (!aKeys.includes(k)) {\n      return false;\n    }\n  }\n  return true;\n}\n\nfunction isObject(thing: unknown): thing is object {\n  return thing !== null && typeof thing === 'object';\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * The amount of milliseconds to exponentially increase.\n */\nconst DEFAULT_INTERVAL_MILLIS = 1000;\n\n/**\n * The factor to backoff by.\n * Should be a number greater than 1.\n */\nconst DEFAULT_BACKOFF_FACTOR = 2;\n\n/**\n * The maximum milliseconds to increase to.\n *\n * <p>Visible for testing\n */\nexport const MAX_VALUE_MILLIS = 4 * 60 * 60 * 1000; // Four hours, like iOS and Android.\n\n/**\n * The percentage of backoff time to randomize by.\n * See\n * http://go/safe-client-behavior#step-1-determine-the-appropriate-retry-interval-to-handle-spike-traffic\n * for context.\n *\n * <p>Visible for testing\n */\nexport const RANDOM_FACTOR = 0.5;\n\n/**\n * Based on the backoff method from\n * https://github.com/google/closure-library/blob/master/closure/goog/math/exponentialbackoff.js.\n * Extracted here so we don't need to pass metadata and a stateful ExponentialBackoff object around.\n */\nexport function calculateBackoffMillis(\n  backoffCount: number,\n  intervalMillis: number = DEFAULT_INTERVAL_MILLIS,\n  backoffFactor: number = DEFAULT_BACKOFF_FACTOR\n): number {\n  // Calculates an exponentially increasing value.\n  // Deviation: calculates value from count and a constant interval, so we only need to save value\n  // and count to restore state.\n  const currBaseValue = intervalMillis * Math.pow(backoffFactor, backoffCount);\n\n  // A random \"fuzz\" to avoid waves of retries.\n  // Deviation: randomFactor is required.\n  const randomWait = Math.round(\n    // A fraction of the backoff value to add/subtract.\n    // Deviation: changes multiplication order to improve readability.\n    RANDOM_FACTOR *\n      currBaseValue *\n      // A random float (rounded to int by Math.round above) in the range [-1, 1]. Determines\n      // if we add or subtract.\n      (Math.random() - 0.5) *\n      2\n  );\n\n  // Limits backoff to max to avoid effectively permanent backoff.\n  return Math.min(MAX_VALUE_MILLIS, currBaseValue + randomWait);\n}\n", "/**\n * @license\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport interface Compat<T> {\n  _delegate: T;\n}\n\nexport function getModularInstance<ExpService>(\n  service: Compat<ExpService> | ExpService\n): ExpService {\n  if (service && (service as Compat<ExpService>)._delegate) {\n    return (service as Compat<ExpService>)._delegate;\n  } else {\n    return service as ExpService;\n  }\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport {\n  InstantiationMode,\n  InstanceFactory,\n  ComponentType,\n  Dictionary,\n  Name,\n  onInstanceCreatedCallback\n} from './types';\n\n/**\n * Component for service name T, e.g. `auth`, `auth-internal`\n */\nexport class Component<T extends Name = Name> {\n  multipleInstances = false;\n  /**\n   * Properties to be added to the service namespace\n   */\n  serviceProps: Dictionary = {};\n\n  instantiationMode = InstantiationMode.LAZY;\n\n  onInstanceCreated: onInstanceCreatedCallback<T> | null = null;\n\n  /**\n   *\n   * @param name The public service name, e.g. app, auth, firestore, database\n   * @param instanceFactory Service factory responsible for creating the public interface\n   * @param type whether the service provided by the component is public or private\n   */\n  constructor(\n    readonly name: T,\n    readonly instanceFactory: InstanceFactory<T>,\n    readonly type: ComponentType\n  ) {}\n\n  setInstantiationMode(mode: InstantiationMode): this {\n    this.instantiationMode = mode;\n    return this;\n  }\n\n  setMultipleInstances(multipleInstances: boolean): this {\n    this.multipleInstances = multipleInstances;\n    return this;\n  }\n\n  setServiceProps(props: Dictionary): this {\n    this.serviceProps = props;\n    return this;\n  }\n\n  setInstanceCreatedCallback(callback: onInstanceCreatedCallback<T>): this {\n    this.onInstanceCreated = callback;\n    return this;\n  }\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport type LogLevelString =\n  | 'debug'\n  | 'verbose'\n  | 'info'\n  | 'warn'\n  | 'error'\n  | 'silent';\n\nexport interface LogOptions {\n  level: LogLevelString;\n}\n\nexport type LogCallback = (callbackParams: LogCallbackParams) => void;\n\nexport interface LogCallbackParams {\n  level: LogLevelString;\n  message: string;\n  args: unknown[];\n  type: string;\n}\n\n/**\n * A container for all of the Logger instances\n */\nexport const instances: Logger[] = [];\n\n/**\n * The JS SDK supports 5 log levels and also allows a user the ability to\n * silence the logs altogether.\n *\n * The order is a follows:\n * DEBUG < VERBOSE < INFO < WARN < ERROR\n *\n * All of the log types above the current log level will be captured (i.e. if\n * you set the log level to `INFO`, errors will still be logged, but `DEBUG` and\n * `VERBOSE` logs will not)\n */\nexport enum LogLevel {\n  DEBUG,\n  VERBOSE,\n  INFO,\n  WARN,\n  ERROR,\n  SILENT\n}\n\nconst levelStringToEnum: { [key in LogLevelString]: LogLevel } = {\n  'debug': LogLevel.DEBUG,\n  'verbose': LogLevel.VERBOSE,\n  'info': LogLevel.INFO,\n  'warn': LogLevel.WARN,\n  'error': LogLevel.ERROR,\n  'silent': LogLevel.SILENT\n};\n\n/**\n * The default log level\n */\nconst defaultLogLevel: LogLevel = LogLevel.INFO;\n\n/**\n * We allow users the ability to pass their own log handler. We will pass the\n * type of log, the current log level, and any other arguments passed (i.e. the\n * messages that the user wants to log) to this function.\n */\nexport type LogHandler = (\n  loggerInstance: Logger,\n  logType: LogLevel,\n  ...args: unknown[]\n) => void;\n\n/**\n * By default, `console.debug` is not displayed in the developer console (in\n * chrome). To avoid forcing users to have to opt-in to these logs twice\n * (i.e. once for firebase, and once in the console), we are sending `DEBUG`\n * logs to the `console.log` function.\n */\nconst ConsoleMethod = {\n  [LogLevel.DEBUG]: 'log',\n  [LogLevel.VERBOSE]: 'log',\n  [LogLevel.INFO]: 'info',\n  [LogLevel.WARN]: 'warn',\n  [LogLevel.ERROR]: 'error'\n};\n\n/**\n * The default log handler will forward DEBUG, VERBOSE, INFO, WARN, and ERROR\n * messages on to their corresponding console counterparts (if the log method\n * is supported by the current log level)\n */\nconst defaultLogHandler: LogHandler = (instance, logType, ...args): void => {\n  if (logType < instance.logLevel) {\n    return;\n  }\n  const now = new Date().toISOString();\n  const method = ConsoleMethod[logType as keyof typeof ConsoleMethod];\n  if (method) {\n    console[method as 'log' | 'info' | 'warn' | 'error'](\n      `[${now}]  ${instance.name}:`,\n      ...args\n    );\n  } else {\n    throw new Error(\n      `Attempted to log a message with an invalid logType (value: ${logType})`\n    );\n  }\n};\n\nexport class Logger {\n  /**\n   * Gives you an instance of a Logger to capture messages according to\n   * Firebase's logging scheme.\n   *\n   * @param name The name that the logs will be associated with\n   */\n  constructor(public name: string) {\n    /**\n     * Capture the current instance for later use\n     */\n    instances.push(this);\n  }\n\n  /**\n   * The log level of the given Logger instance.\n   */\n  private _logLevel = defaultLogLevel;\n\n  get logLevel(): LogLevel {\n    return this._logLevel;\n  }\n\n  set logLevel(val: LogLevel) {\n    if (!(val in LogLevel)) {\n      throw new TypeError(`Invalid value \"${val}\" assigned to \\`logLevel\\``);\n    }\n    this._logLevel = val;\n  }\n\n  // Workaround for setter/getter having to be the same type.\n  setLogLevel(val: LogLevel | LogLevelString): void {\n    this._logLevel = typeof val === 'string' ? levelStringToEnum[val] : val;\n  }\n\n  /**\n   * The main (internal) log handler for the Logger instance.\n   * Can be set to a new function in internal package code but not by user.\n   */\n  private _logHandler: LogHandler = defaultLogHandler;\n  get logHandler(): LogHandler {\n    return this._logHandler;\n  }\n  set logHandler(val: LogHandler) {\n    if (typeof val !== 'function') {\n      throw new TypeError('Value assigned to `logHandler` must be a function');\n    }\n    this._logHandler = val;\n  }\n\n  /**\n   * The optional, additional, user-defined log handler for the Logger instance.\n   */\n  private _userLogHandler: LogHandler | null = null;\n  get userLogHandler(): LogHandler | null {\n    return this._userLogHandler;\n  }\n  set userLogHandler(val: LogHandler | null) {\n    this._userLogHandler = val;\n  }\n\n  /**\n   * The functions below are all based on the `console` interface\n   */\n\n  debug(...args: unknown[]): void {\n    this._userLogHandler && this._userLogHandler(this, LogLevel.DEBUG, ...args);\n    this._logHandler(this, LogLevel.DEBUG, ...args);\n  }\n  log(...args: unknown[]): void {\n    this._userLogHandler &&\n      this._userLogHandler(this, LogLevel.VERBOSE, ...args);\n    this._logHandler(this, LogLevel.VERBOSE, ...args);\n  }\n  info(...args: unknown[]): void {\n    this._userLogHandler && this._userLogHandler(this, LogLevel.INFO, ...args);\n    this._logHandler(this, LogLevel.INFO, ...args);\n  }\n  warn(...args: unknown[]): void {\n    this._userLogHandler && this._userLogHandler(this, LogLevel.WARN, ...args);\n    this._logHandler(this, LogLevel.WARN, ...args);\n  }\n  error(...args: unknown[]): void {\n    this._userLogHandler && this._userLogHandler(this, LogLevel.ERROR, ...args);\n    this._logHandler(this, LogLevel.ERROR, ...args);\n  }\n}\n\nexport function setLogLevel(level: LogLevelString | LogLevel): void {\n  instances.forEach(inst => {\n    inst.setLogLevel(level);\n  });\n}\n\nexport function setUserLogHandler(\n  logCallback: LogCallback | null,\n  options?: LogOptions\n): void {\n  for (const instance of instances) {\n    let customLogLevel: LogLevel | null = null;\n    if (options && options.level) {\n      customLogLevel = levelStringToEnum[options.level];\n    }\n    if (logCallback === null) {\n      instance.userLogHandler = null;\n    } else {\n      instance.userLogHandler = (\n        instance: Logger,\n        level: LogLevel,\n        ...args: unknown[]\n      ) => {\n        const message = args\n          .map(arg => {\n            if (arg == null) {\n              return null;\n            } else if (typeof arg === 'string') {\n              return arg;\n            } else if (typeof arg === 'number' || typeof arg === 'boolean') {\n              return arg.toString();\n            } else if (arg instanceof Error) {\n              return arg.message;\n            } else {\n              try {\n                return JSON.stringify(arg);\n              } catch (ignored) {\n                return null;\n              }\n            }\n          })\n          .filter(arg => arg)\n          .join(' ');\n        if (level >= (customLogLevel ?? instance.logLevel)) {\n          logCallback({\n            level: LogLevel[level].toLowerCase() as LogLevelString,\n            message,\n            args,\n            type: instance.name\n          });\n        }\n      };\n    }\n  }\n}\n", "const instanceOfAny = (object, constructors) => constructors.some((c) => object instanceof c);\n\nlet idbProxyableTypes;\nlet cursorAdvanceMethods;\n// This is a function to prevent it throwing up in node environments.\nfunction getIdbProxyableTypes() {\n    return (idbProxyableTypes ||\n        (idbProxyableTypes = [\n            IDBDatabase,\n            IDBObjectStore,\n            IDBIndex,\n            IDBCursor,\n            IDBTransaction,\n        ]));\n}\n// This is a function to prevent it throwing up in node environments.\nfunction getCursorAdvanceMethods() {\n    return (cursorAdvanceMethods ||\n        (cursorAdvanceMethods = [\n            IDBCursor.prototype.advance,\n            IDBCursor.prototype.continue,\n            IDBCursor.prototype.continuePrimaryKey,\n        ]));\n}\nconst cursorRequestMap = new WeakMap();\nconst transactionDoneMap = new WeakMap();\nconst transactionStoreNamesMap = new WeakMap();\nconst transformCache = new WeakMap();\nconst reverseTransformCache = new WeakMap();\nfunction promisifyRequest(request) {\n    const promise = new Promise((resolve, reject) => {\n        const unlisten = () => {\n            request.removeEventListener('success', success);\n            request.removeEventListener('error', error);\n        };\n        const success = () => {\n            resolve(wrap(request.result));\n            unlisten();\n        };\n        const error = () => {\n            reject(request.error);\n            unlisten();\n        };\n        request.addEventListener('success', success);\n        request.addEventListener('error', error);\n    });\n    promise\n        .then((value) => {\n        // Since cursoring reuses the IDBRequest (*sigh*), we cache it for later retrieval\n        // (see wrapFunction).\n        if (value instanceof IDBCursor) {\n            cursorRequestMap.set(value, request);\n        }\n        // Catching to avoid \"Uncaught Promise exceptions\"\n    })\n        .catch(() => { });\n    // This mapping exists in reverseTransformCache but doesn't doesn't exist in transformCache. This\n    // is because we create many promises from a single IDBRequest.\n    reverseTransformCache.set(promise, request);\n    return promise;\n}\nfunction cacheDonePromiseForTransaction(tx) {\n    // Early bail if we've already created a done promise for this transaction.\n    if (transactionDoneMap.has(tx))\n        return;\n    const done = new Promise((resolve, reject) => {\n        const unlisten = () => {\n            tx.removeEventListener('complete', complete);\n            tx.removeEventListener('error', error);\n            tx.removeEventListener('abort', error);\n        };\n        const complete = () => {\n            resolve();\n            unlisten();\n        };\n        const error = () => {\n            reject(tx.error || new DOMException('AbortError', 'AbortError'));\n            unlisten();\n        };\n        tx.addEventListener('complete', complete);\n        tx.addEventListener('error', error);\n        tx.addEventListener('abort', error);\n    });\n    // Cache it for later retrieval.\n    transactionDoneMap.set(tx, done);\n}\nlet idbProxyTraps = {\n    get(target, prop, receiver) {\n        if (target instanceof IDBTransaction) {\n            // Special handling for transaction.done.\n            if (prop === 'done')\n                return transactionDoneMap.get(target);\n            // Polyfill for objectStoreNames because of Edge.\n            if (prop === 'objectStoreNames') {\n                return target.objectStoreNames || transactionStoreNamesMap.get(target);\n            }\n            // Make tx.store return the only store in the transaction, or undefined if there are many.\n            if (prop === 'store') {\n                return receiver.objectStoreNames[1]\n                    ? undefined\n                    : receiver.objectStore(receiver.objectStoreNames[0]);\n            }\n        }\n        // Else transform whatever we get back.\n        return wrap(target[prop]);\n    },\n    set(target, prop, value) {\n        target[prop] = value;\n        return true;\n    },\n    has(target, prop) {\n        if (target instanceof IDBTransaction &&\n            (prop === 'done' || prop === 'store')) {\n            return true;\n        }\n        return prop in target;\n    },\n};\nfunction replaceTraps(callback) {\n    idbProxyTraps = callback(idbProxyTraps);\n}\nfunction wrapFunction(func) {\n    // Due to expected object equality (which is enforced by the caching in `wrap`), we\n    // only create one new func per func.\n    // Edge doesn't support objectStoreNames (booo), so we polyfill it here.\n    if (func === IDBDatabase.prototype.transaction &&\n        !('objectStoreNames' in IDBTransaction.prototype)) {\n        return function (storeNames, ...args) {\n            const tx = func.call(unwrap(this), storeNames, ...args);\n            transactionStoreNamesMap.set(tx, storeNames.sort ? storeNames.sort() : [storeNames]);\n            return wrap(tx);\n        };\n    }\n    // Cursor methods are special, as the behaviour is a little more different to standard IDB. In\n    // IDB, you advance the cursor and wait for a new 'success' on the IDBRequest that gave you the\n    // cursor. It's kinda like a promise that can resolve with many values. That doesn't make sense\n    // with real promises, so each advance methods returns a new promise for the cursor object, or\n    // undefined if the end of the cursor has been reached.\n    if (getCursorAdvanceMethods().includes(func)) {\n        return function (...args) {\n            // Calling the original function with the proxy as 'this' causes ILLEGAL INVOCATION, so we use\n            // the original object.\n            func.apply(unwrap(this), args);\n            return wrap(cursorRequestMap.get(this));\n        };\n    }\n    return function (...args) {\n        // Calling the original function with the proxy as 'this' causes ILLEGAL INVOCATION, so we use\n        // the original object.\n        return wrap(func.apply(unwrap(this), args));\n    };\n}\nfunction transformCachableValue(value) {\n    if (typeof value === 'function')\n        return wrapFunction(value);\n    // This doesn't return, it just creates a 'done' promise for the transaction,\n    // which is later returned for transaction.done (see idbObjectHandler).\n    if (value instanceof IDBTransaction)\n        cacheDonePromiseForTransaction(value);\n    if (instanceOfAny(value, getIdbProxyableTypes()))\n        return new Proxy(value, idbProxyTraps);\n    // Return the same value back if we're not going to transform it.\n    return value;\n}\nfunction wrap(value) {\n    // We sometimes generate multiple promises from a single IDBRequest (eg when cursoring), because\n    // IDB is weird and a single IDBRequest can yield many responses, so these can't be cached.\n    if (value instanceof IDBRequest)\n        return promisifyRequest(value);\n    // If we've already transformed this value before, reuse the transformed value.\n    // This is faster, but it also provides object equality.\n    if (transformCache.has(value))\n        return transformCache.get(value);\n    const newValue = transformCachableValue(value);\n    // Not all types are transformed.\n    // These may be primitive types, so they can't be WeakMap keys.\n    if (newValue !== value) {\n        transformCache.set(value, newValue);\n        reverseTransformCache.set(newValue, value);\n    }\n    return newValue;\n}\nconst unwrap = (value) => reverseTransformCache.get(value);\n\nexport { reverseTransformCache as a, instanceOfAny as i, replaceTraps as r, unwrap as u, wrap as w };\n", "import { w as wrap, r as replaceTraps } from './wrap-idb-value.js';\nexport { u as unwrap, w as wrap } from './wrap-idb-value.js';\n\n/**\n * Open a database.\n *\n * @param name Name of the database.\n * @param version Schema version.\n * @param callbacks Additional callbacks.\n */\nfunction openDB(name, version, { blocked, upgrade, blocking, terminated } = {}) {\n    const request = indexedDB.open(name, version);\n    const openPromise = wrap(request);\n    if (upgrade) {\n        request.addEventListener('upgradeneeded', (event) => {\n            upgrade(wrap(request.result), event.oldVersion, event.newVersion, wrap(request.transaction), event);\n        });\n    }\n    if (blocked) {\n        request.addEventListener('blocked', (event) => blocked(\n        // Casting due to https://github.com/microsoft/TypeScript-DOM-lib-generator/pull/1405\n        event.oldVersion, event.newVersion, event));\n    }\n    openPromise\n        .then((db) => {\n        if (terminated)\n            db.addEventListener('close', () => terminated());\n        if (blocking) {\n            db.addEventListener('versionchange', (event) => blocking(event.oldVersion, event.newVersion, event));\n        }\n    })\n        .catch(() => { });\n    return openPromise;\n}\n/**\n * Delete a database.\n *\n * @param name Name of the database.\n */\nfunction deleteDB(name, { blocked } = {}) {\n    const request = indexedDB.deleteDatabase(name);\n    if (blocked) {\n        request.addEventListener('blocked', (event) => blocked(\n        // Casting due to https://github.com/microsoft/TypeScript-DOM-lib-generator/pull/1405\n        event.oldVersion, event));\n    }\n    return wrap(request).then(() => undefined);\n}\n\nconst readMethods = ['get', 'getKey', 'getAll', 'getAllKeys', 'count'];\nconst writeMethods = ['put', 'add', 'delete', 'clear'];\nconst cachedMethods = new Map();\nfunction getMethod(target, prop) {\n    if (!(target instanceof IDBDatabase &&\n        !(prop in target) &&\n        typeof prop === 'string')) {\n        return;\n    }\n    if (cachedMethods.get(prop))\n        return cachedMethods.get(prop);\n    const targetFuncName = prop.replace(/FromIndex$/, '');\n    const useIndex = prop !== targetFuncName;\n    const isWrite = writeMethods.includes(targetFuncName);\n    if (\n    // Bail if the target doesn't exist on the target. Eg, getAll isn't in Edge.\n    !(targetFuncName in (useIndex ? IDBIndex : IDBObjectStore).prototype) ||\n        !(isWrite || readMethods.includes(targetFuncName))) {\n        return;\n    }\n    const method = async function (storeName, ...args) {\n        // isWrite ? 'readwrite' : undefined gzipps better, but fails in Edge :(\n        const tx = this.transaction(storeName, isWrite ? 'readwrite' : 'readonly');\n        let target = tx.store;\n        if (useIndex)\n            target = target.index(args.shift());\n        // Must reject if op rejects.\n        // If it's a write operation, must reject if tx.done rejects.\n        // Must reject with op rejection first.\n        // Must resolve with op value.\n        // Must handle both promises (no unhandled rejections)\n        return (await Promise.all([\n            target[targetFuncName](...args),\n            isWrite && tx.done,\n        ]))[0];\n    };\n    cachedMethods.set(prop, method);\n    return method;\n}\nreplaceTraps((oldTraps) => ({\n    ...oldTraps,\n    get: (target, prop, receiver) => getMethod(target, prop) || oldTraps.get(target, prop, receiver),\n    has: (target, prop) => !!getMethod(target, prop) || oldTraps.has(target, prop),\n}));\n\nexport { deleteDB, openDB };\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { version } from '../../package.json';\n\nexport const PENDING_TIMEOUT_MS = 10000;\n\nexport const PACKAGE_VERSION = `w:${version}`;\nexport const INTERNAL_AUTH_VERSION = 'FIS_v2';\n\nexport const INSTALLATIONS_API_URL =\n  'https://firebaseinstallations.googleapis.com/v1';\n\nexport const TOKEN_EXPIRATION_BUFFER = 60 * 60 * 1000; // One hour\n\nexport const SERVICE = 'installations';\nexport const SERVICE_NAME = 'Installations';\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { ErrorFactory, FirebaseError } from '@firebase/util';\nimport { SERVICE, SERVICE_NAME } from './constants';\n\nexport const enum ErrorCode {\n  MISSING_APP_CONFIG_VALUES = 'missing-app-config-values',\n  NOT_REGISTERED = 'not-registered',\n  INSTALLATION_NOT_FOUND = 'installation-not-found',\n  REQUEST_FAILED = 'request-failed',\n  APP_OFFLINE = 'app-offline',\n  DELETE_PENDING_REGISTRATION = 'delete-pending-registration'\n}\n\nconst ERROR_DESCRIPTION_MAP: { readonly [key in ErrorCode]: string } = {\n  [ErrorCode.MISSING_APP_CONFIG_VALUES]:\n    'Missing App configuration value: \"{$valueName}\"',\n  [ErrorCode.NOT_REGISTERED]: 'Firebase Installation is not registered.',\n  [ErrorCode.INSTALLATION_NOT_FOUND]: 'Firebase Installation not found.',\n  [ErrorCode.REQUEST_FAILED]:\n    '{$requestName} request failed with error \"{$serverCode} {$serverStatus}: {$serverMessage}\"',\n  [ErrorCode.APP_OFFLINE]: 'Could not process request. Application offline.',\n  [ErrorCode.DELETE_PENDING_REGISTRATION]:\n    \"Can't delete installation while there is a pending registration request.\"\n};\n\ninterface ErrorParams {\n  [ErrorCode.MISSING_APP_CONFIG_VALUES]: {\n    valueName: string;\n  };\n  [ErrorCode.REQUEST_FAILED]: {\n    requestName: string;\n    [index: string]: string | number; // to make TypeScript 3.8 happy\n  } & ServerErrorData;\n}\n\nexport const ERROR_FACTORY = new ErrorFactory<ErrorCode, ErrorParams>(\n  SERVICE,\n  SERVICE_NAME,\n  ERROR_DESCRIPTION_MAP\n);\n\nexport interface ServerErrorData {\n  serverCode: number;\n  serverMessage: string;\n  serverStatus: string;\n}\n\nexport type ServerError = FirebaseError & { customData: ServerErrorData };\n\n/** Returns true if error is a FirebaseError that is based on an error from the server. */\nexport function isServerError(error: unknown): error is ServerError {\n  return (\n    error instanceof FirebaseError &&\n    error.code.includes(ErrorCode.REQUEST_FAILED)\n  );\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { FirebaseError } from '@firebase/util';\nimport { GenerateAuthTokenResponse } from '../interfaces/api-response';\nimport {\n  CompletedAuthToken,\n  RegisteredInstallationEntry,\n  RequestStatus\n} from '../interfaces/installation-entry';\nimport {\n  INSTALLATIONS_API_URL,\n  INTERNAL_AUTH_VERSION\n} from '../util/constants';\nimport { ERROR_FACTORY, ErrorCode } from '../util/errors';\nimport { AppConfig } from '../interfaces/installation-impl';\n\nexport function getInstallationsEndpoint({ projectId }: AppConfig): string {\n  return `${INSTALLATIONS_API_URL}/projects/${projectId}/installations`;\n}\n\nexport function extractAuthTokenInfoFromResponse(\n  response: GenerateAuthTokenResponse\n): CompletedAuthToken {\n  return {\n    token: response.token,\n    requestStatus: RequestStatus.COMPLETED,\n    expiresIn: getExpiresInFromResponseExpiresIn(response.expiresIn),\n    creationTime: Date.now()\n  };\n}\n\nexport async function getErrorFromResponse(\n  requestName: string,\n  response: Response\n): Promise<FirebaseError> {\n  const responseJson: ErrorResponse = await response.json();\n  const errorData = responseJson.error;\n  return ERROR_FACTORY.create(ErrorCode.REQUEST_FAILED, {\n    requestName,\n    serverCode: errorData.code,\n    serverMessage: errorData.message,\n    serverStatus: errorData.status\n  });\n}\n\nexport function getHeaders({ apiKey }: AppConfig): Headers {\n  return new Headers({\n    'Content-Type': 'application/json',\n    Accept: 'application/json',\n    'x-goog-api-key': apiKey\n  });\n}\n\nexport function getHeadersWithAuth(\n  appConfig: AppConfig,\n  { refreshToken }: RegisteredInstallationEntry\n): Headers {\n  const headers = getHeaders(appConfig);\n  headers.append('Authorization', getAuthorizationHeader(refreshToken));\n  return headers;\n}\n\nexport interface ErrorResponse {\n  error: {\n    code: number;\n    message: string;\n    status: string;\n  };\n}\n\n/**\n * Calls the passed in fetch wrapper and returns the response.\n * If the returned response has a status of 5xx, re-runs the function once and\n * returns the response.\n */\nexport async function retryIfServerError(\n  fn: () => Promise<Response>\n): Promise<Response> {\n  const result = await fn();\n\n  if (result.status >= 500 && result.status < 600) {\n    // Internal Server Error. Retry request.\n    return fn();\n  }\n\n  return result;\n}\n\nfunction getExpiresInFromResponseExpiresIn(responseExpiresIn: string): number {\n  // This works because the server will never respond with fractions of a second.\n  return Number(responseExpiresIn.replace('s', '000'));\n}\n\nfunction getAuthorizationHeader(refreshToken: string): string {\n  return `${INTERNAL_AUTH_VERSION} ${refreshToken}`;\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/** Returns a promise that resolves after given time passes. */\nexport function sleep(ms: number): Promise<void> {\n  return new Promise<void>(resolve => {\n    setTimeout(resolve, ms);\n  });\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { bufferToBase64UrlSafe } from './buffer-to-base64-url-safe';\n\nexport const VALID_FID_PATTERN = /^[cdef][\\w-]{21}$/;\nexport const INVALID_FID = '';\n\n/**\n * Generates a new FID using random values from Web Crypto API.\n * Returns an empty string if FID generation fails for any reason.\n */\nexport function generateFid(): string {\n  try {\n    // A valid FID has exactly 22 base64 characters, which is 132 bits, or 16.5\n    // bytes. our implementation generates a 17 byte array instead.\n    const fidByteArray = new Uint8Array(17);\n    const crypto =\n      self.crypto || (self as unknown as { msCrypto: Crypto }).msCrypto;\n    crypto.getRandomValues(fidByteArray);\n\n    // Replace the first 4 random bits with the constant FID header of 0b0111.\n    fidByteArray[0] = 0b01110000 + (fidByteArray[0] % 0b00010000);\n\n    const fid = encode(fidByteArray);\n\n    return VALID_FID_PATTERN.test(fid) ? fid : INVALID_FID;\n  } catch {\n    // FID generation errored\n    return INVALID_FID;\n  }\n}\n\n/** Converts a FID Uint8Array to a base64 string representation. */\nfunction encode(fidByteArray: Uint8Array): string {\n  const b64String = bufferToBase64UrlSafe(fidByteArray);\n\n  // Remove the 23rd character that was added because of the extra 4 bits at the\n  // end of our 17 byte array, and the '=' padding.\n  return b64String.substr(0, 22);\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport function bufferToBase64UrlSafe(array: Uint8Array): string {\n  const b64 = btoa(String.fromCharCode(...array));\n  return b64.replace(/\\+/g, '-').replace(/\\//g, '_');\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { AppConfig } from '../interfaces/installation-impl';\n\n/** Returns a string key that can be used to identify the app. */\nexport function getKey(appConfig: AppConfig): string {\n  return `${appConfig.appName}!${appConfig.appId}`;\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { getKey } from '../util/get-key';\nimport { AppConfig } from '../interfaces/installation-impl';\nimport { IdChangeCallbackFn } from '../api';\n\nconst fidChangeCallbacks: Map<string, Set<IdChangeCallbackFn>> = new Map();\n\n/**\n * Calls the onIdChange callbacks with the new FID value, and broadcasts the\n * change to other tabs.\n */\nexport function fidChanged(appConfig: AppConfig, fid: string): void {\n  const key = getKey(appConfig);\n\n  callFidChangeCallbacks(key, fid);\n  broadcastFidChange(key, fid);\n}\n\nexport function addCallback(\n  appConfig: AppConfig,\n  callback: IdChangeCallbackFn\n): void {\n  // Open the broadcast channel if it's not already open,\n  // to be able to listen to change events from other tabs.\n  getBroadcastChannel();\n\n  const key = getKey(appConfig);\n\n  let callbackSet = fidChangeCallbacks.get(key);\n  if (!callbackSet) {\n    callbackSet = new Set();\n    fidChangeCallbacks.set(key, callbackSet);\n  }\n  callbackSet.add(callback);\n}\n\nexport function removeCallback(\n  appConfig: AppConfig,\n  callback: IdChangeCallbackFn\n): void {\n  const key = getKey(appConfig);\n\n  const callbackSet = fidChangeCallbacks.get(key);\n\n  if (!callbackSet) {\n    return;\n  }\n\n  callbackSet.delete(callback);\n  if (callbackSet.size === 0) {\n    fidChangeCallbacks.delete(key);\n  }\n\n  // Close broadcast channel if there are no more callbacks.\n  closeBroadcastChannel();\n}\n\nfunction callFidChangeCallbacks(key: string, fid: string): void {\n  const callbacks = fidChangeCallbacks.get(key);\n  if (!callbacks) {\n    return;\n  }\n\n  for (const callback of callbacks) {\n    callback(fid);\n  }\n}\n\nfunction broadcastFidChange(key: string, fid: string): void {\n  const channel = getBroadcastChannel();\n  if (channel) {\n    channel.postMessage({ key, fid });\n  }\n  closeBroadcastChannel();\n}\n\nlet broadcastChannel: BroadcastChannel | null = null;\n/** Opens and returns a BroadcastChannel if it is supported by the browser. */\nfunction getBroadcastChannel(): BroadcastChannel | null {\n  if (!broadcastChannel && 'BroadcastChannel' in self) {\n    broadcastChannel = new BroadcastChannel('[Firebase] FID Change');\n    broadcastChannel.onmessage = e => {\n      callFidChangeCallbacks(e.data.key, e.data.fid);\n    };\n  }\n  return broadcastChannel;\n}\n\nfunction closeBroadcastChannel(): void {\n  if (fidChangeCallbacks.size === 0 && broadcastChannel) {\n    broadcastChannel.close();\n    broadcastChannel = null;\n  }\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { DBSchema, IDBPDatabase, openDB } from 'idb';\nimport { AppConfig } from '../interfaces/installation-impl';\nimport { InstallationEntry } from '../interfaces/installation-entry';\nimport { getKey } from '../util/get-key';\nimport { fidChanged } from './fid-changed';\n\nconst DATABASE_NAME = 'firebase-installations-database';\nconst DATABASE_VERSION = 1;\nconst OBJECT_STORE_NAME = 'firebase-installations-store';\n\ninterface InstallationsDB extends DBSchema {\n  'firebase-installations-store': {\n    key: string;\n    value: InstallationEntry | undefined;\n  };\n}\n\nlet dbPromise: Promise<IDBPDatabase<InstallationsDB>> | null = null;\nfunction getDbPromise(): Promise<IDBPDatabase<InstallationsDB>> {\n  if (!dbPromise) {\n    dbPromise = openDB(DATABASE_NAME, DATABASE_VERSION, {\n      upgrade: (db, oldVersion) => {\n        // We don't use 'break' in this switch statement, the fall-through\n        // behavior is what we want, because if there are multiple versions between\n        // the old version and the current version, we want ALL the migrations\n        // that correspond to those versions to run, not only the last one.\n        // eslint-disable-next-line default-case\n        switch (oldVersion) {\n          case 0:\n            db.createObjectStore(OBJECT_STORE_NAME);\n        }\n      }\n    });\n  }\n  return dbPromise;\n}\n\n/** Gets record(s) from the objectStore that match the given key. */\nexport async function get(\n  appConfig: AppConfig\n): Promise<InstallationEntry | undefined> {\n  const key = getKey(appConfig);\n  const db = await getDbPromise();\n  return db\n    .transaction(OBJECT_STORE_NAME)\n    .objectStore(OBJECT_STORE_NAME)\n    .get(key) as Promise<InstallationEntry>;\n}\n\n/** Assigns or overwrites the record for the given key with the given value. */\nexport async function set<ValueType extends InstallationEntry>(\n  appConfig: AppConfig,\n  value: ValueType\n): Promise<ValueType> {\n  const key = getKey(appConfig);\n  const db = await getDbPromise();\n  const tx = db.transaction(OBJECT_STORE_NAME, 'readwrite');\n  const objectStore = tx.objectStore(OBJECT_STORE_NAME);\n  const oldValue = (await objectStore.get(key)) as InstallationEntry;\n  await objectStore.put(value, key);\n  await tx.done;\n\n  if (!oldValue || oldValue.fid !== value.fid) {\n    fidChanged(appConfig, value.fid);\n  }\n\n  return value;\n}\n\n/** Removes record(s) from the objectStore that match the given key. */\nexport async function remove(appConfig: AppConfig): Promise<void> {\n  const key = getKey(appConfig);\n  const db = await getDbPromise();\n  const tx = db.transaction(OBJECT_STORE_NAME, 'readwrite');\n  await tx.objectStore(OBJECT_STORE_NAME).delete(key);\n  await tx.done;\n}\n\n/**\n * Atomically updates a record with the result of updateFn, which gets\n * called with the current value. If newValue is undefined, the record is\n * deleted instead.\n * @return Updated value\n */\nexport async function update<ValueType extends InstallationEntry | undefined>(\n  appConfig: AppConfig,\n  updateFn: (previousValue: InstallationEntry | undefined) => ValueType\n): Promise<ValueType> {\n  const key = getKey(appConfig);\n  const db = await getDbPromise();\n  const tx = db.transaction(OBJECT_STORE_NAME, 'readwrite');\n  const store = tx.objectStore(OBJECT_STORE_NAME);\n  const oldValue: InstallationEntry | undefined = (await store.get(\n    key\n  )) as InstallationEntry;\n  const newValue = updateFn(oldValue);\n\n  if (newValue === undefined) {\n    await store.delete(key);\n  } else {\n    await store.put(newValue, key);\n  }\n  await tx.done;\n\n  if (newValue && (!oldValue || oldValue.fid !== newValue.fid)) {\n    fidChanged(appConfig, newValue.fid);\n  }\n\n  return newValue;\n}\n\nexport async function clear(): Promise<void> {\n  const db = await getDbPromise();\n  const tx = db.transaction(OBJECT_STORE_NAME, 'readwrite');\n  await tx.objectStore(OBJECT_STORE_NAME).clear();\n  await tx.done;\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { createInstallationRequest } from '../functions/create-installation-request';\nimport {\n  AppConfig,\n  FirebaseInstallationsImpl\n} from '../interfaces/installation-impl';\nimport {\n  InProgressInstallationEntry,\n  InstallationEntry,\n  RegisteredInstallationEntry,\n  RequestStatus\n} from '../interfaces/installation-entry';\nimport { PENDING_TIMEOUT_MS } from '../util/constants';\nimport { ERROR_FACTORY, ErrorCode, isServerError } from '../util/errors';\nimport { sleep } from '../util/sleep';\nimport { generateFid, INVALID_FID } from './generate-fid';\nimport { remove, set, update } from './idb-manager';\n\nexport interface InstallationEntryWithRegistrationPromise {\n  installationEntry: InstallationEntry;\n  /** Exist iff the installationEntry is not registered. */\n  registrationPromise?: Promise<RegisteredInstallationEntry>;\n}\n\n/**\n * Updates and returns the InstallationEntry from the database.\n * Also triggers a registration request if it is necessary and possible.\n */\nexport async function getInstallationEntry(\n  installations: FirebaseInstallationsImpl\n): Promise<InstallationEntryWithRegistrationPromise> {\n  let registrationPromise: Promise<RegisteredInstallationEntry> | undefined;\n\n  const installationEntry = await update(installations.appConfig, oldEntry => {\n    const installationEntry = updateOrCreateInstallationEntry(oldEntry);\n    const entryWithPromise = triggerRegistrationIfNecessary(\n      installations,\n      installationEntry\n    );\n    registrationPromise = entryWithPromise.registrationPromise;\n    return entryWithPromise.installationEntry;\n  });\n\n  if (installationEntry.fid === INVALID_FID) {\n    // FID generation failed. Waiting for the FID from the server.\n    return { installationEntry: await registrationPromise! };\n  }\n\n  return {\n    installationEntry,\n    registrationPromise\n  };\n}\n\n/**\n * Creates a new Installation Entry if one does not exist.\n * Also clears timed out pending requests.\n */\nfunction updateOrCreateInstallationEntry(\n  oldEntry: InstallationEntry | undefined\n): InstallationEntry {\n  const entry: InstallationEntry = oldEntry || {\n    fid: generateFid(),\n    registrationStatus: RequestStatus.NOT_STARTED\n  };\n\n  return clearTimedOutRequest(entry);\n}\n\n/**\n * If the Firebase Installation is not registered yet, this will trigger the\n * registration and return an InProgressInstallationEntry.\n *\n * If registrationPromise does not exist, the installationEntry is guaranteed\n * to be registered.\n */\nfunction triggerRegistrationIfNecessary(\n  installations: FirebaseInstallationsImpl,\n  installationEntry: InstallationEntry\n): InstallationEntryWithRegistrationPromise {\n  if (installationEntry.registrationStatus === RequestStatus.NOT_STARTED) {\n    if (!navigator.onLine) {\n      // Registration required but app is offline.\n      const registrationPromiseWithError = Promise.reject(\n        ERROR_FACTORY.create(ErrorCode.APP_OFFLINE)\n      );\n      return {\n        installationEntry,\n        registrationPromise: registrationPromiseWithError\n      };\n    }\n\n    // Try registering. Change status to IN_PROGRESS.\n    const inProgressEntry: InProgressInstallationEntry = {\n      fid: installationEntry.fid,\n      registrationStatus: RequestStatus.IN_PROGRESS,\n      registrationTime: Date.now()\n    };\n    const registrationPromise = registerInstallation(\n      installations,\n      inProgressEntry\n    );\n    return { installationEntry: inProgressEntry, registrationPromise };\n  } else if (\n    installationEntry.registrationStatus === RequestStatus.IN_PROGRESS\n  ) {\n    return {\n      installationEntry,\n      registrationPromise: waitUntilFidRegistration(installations)\n    };\n  } else {\n    return { installationEntry };\n  }\n}\n\n/** This will be executed only once for each new Firebase Installation. */\nasync function registerInstallation(\n  installations: FirebaseInstallationsImpl,\n  installationEntry: InProgressInstallationEntry\n): Promise<RegisteredInstallationEntry> {\n  try {\n    const registeredInstallationEntry = await createInstallationRequest(\n      installations,\n      installationEntry\n    );\n    return set(installations.appConfig, registeredInstallationEntry);\n  } catch (e) {\n    if (isServerError(e) && e.customData.serverCode === 409) {\n      // Server returned a \"FID cannot be used\" error.\n      // Generate a new ID next time.\n      await remove(installations.appConfig);\n    } else {\n      // Registration failed. Set FID as not registered.\n      await set(installations.appConfig, {\n        fid: installationEntry.fid,\n        registrationStatus: RequestStatus.NOT_STARTED\n      });\n    }\n    throw e;\n  }\n}\n\n/** Call if FID registration is pending in another request. */\nasync function waitUntilFidRegistration(\n  installations: FirebaseInstallationsImpl\n): Promise<RegisteredInstallationEntry> {\n  // Unfortunately, there is no way of reliably observing when a value in\n  // IndexedDB changes (yet, see https://github.com/WICG/indexed-db-observers),\n  // so we need to poll.\n\n  let entry: InstallationEntry = await updateInstallationRequest(\n    installations.appConfig\n  );\n  while (entry.registrationStatus === RequestStatus.IN_PROGRESS) {\n    // createInstallation request still in progress.\n    await sleep(100);\n\n    entry = await updateInstallationRequest(installations.appConfig);\n  }\n\n  if (entry.registrationStatus === RequestStatus.NOT_STARTED) {\n    // The request timed out or failed in a different call. Try again.\n    const { installationEntry, registrationPromise } =\n      await getInstallationEntry(installations);\n\n    if (registrationPromise) {\n      return registrationPromise;\n    } else {\n      // if there is no registrationPromise, entry is registered.\n      return installationEntry as RegisteredInstallationEntry;\n    }\n  }\n\n  return entry;\n}\n\n/**\n * Called only if there is a CreateInstallation request in progress.\n *\n * Updates the InstallationEntry in the DB based on the status of the\n * CreateInstallation request.\n *\n * Returns the updated InstallationEntry.\n */\nfunction updateInstallationRequest(\n  appConfig: AppConfig\n): Promise<InstallationEntry> {\n  return update(appConfig, oldEntry => {\n    if (!oldEntry) {\n      throw ERROR_FACTORY.create(ErrorCode.INSTALLATION_NOT_FOUND);\n    }\n    return clearTimedOutRequest(oldEntry);\n  });\n}\n\nfunction clearTimedOutRequest(entry: InstallationEntry): InstallationEntry {\n  if (hasInstallationRequestTimedOut(entry)) {\n    return {\n      fid: entry.fid,\n      registrationStatus: RequestStatus.NOT_STARTED\n    };\n  }\n\n  return entry;\n}\n\nfunction hasInstallationRequestTimedOut(\n  installationEntry: InstallationEntry\n): boolean {\n  return (\n    installationEntry.registrationStatus === RequestStatus.IN_PROGRESS &&\n    installationEntry.registrationTime + PENDING_TIMEOUT_MS < Date.now()\n  );\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { CreateInstallationResponse } from '../interfaces/api-response';\nimport {\n  InProgressInstallationEntry,\n  RegisteredInstallationEntry,\n  RequestStatus\n} from '../interfaces/installation-entry';\nimport { INTERNAL_AUTH_VERSION, PACKAGE_VERSION } from '../util/constants';\nimport {\n  extractAuthTokenInfoFromResponse,\n  getErrorFromResponse,\n  getHeaders,\n  getInstallationsEndpoint,\n  retryIfServerError\n} from './common';\nimport { FirebaseInstallationsImpl } from '../interfaces/installation-impl';\n\nexport async function createInstallationRequest(\n  { appConfig, heartbeatServiceProvider }: FirebaseInstallationsImpl,\n  { fid }: InProgressInstallationEntry\n): Promise<RegisteredInstallationEntry> {\n  const endpoint = getInstallationsEndpoint(appConfig);\n\n  const headers = getHeaders(appConfig);\n\n  // If heartbeat service exists, add the heartbeat string to the header.\n  const heartbeatService = heartbeatServiceProvider.getImmediate({\n    optional: true\n  });\n  if (heartbeatService) {\n    const heartbeatsHeader = await heartbeatService.getHeartbeatsHeader();\n    if (heartbeatsHeader) {\n      headers.append('x-firebase-client', heartbeatsHeader);\n    }\n  }\n\n  const body = {\n    fid,\n    authVersion: INTERNAL_AUTH_VERSION,\n    appId: appConfig.appId,\n    sdkVersion: PACKAGE_VERSION\n  };\n\n  const request: RequestInit = {\n    method: 'POST',\n    headers,\n    body: JSON.stringify(body)\n  };\n\n  const response = await retryIfServerError(() => fetch(endpoint, request));\n  if (response.ok) {\n    const responseValue: CreateInstallationResponse = await response.json();\n    const registeredInstallationEntry: RegisteredInstallationEntry = {\n      fid: responseValue.fid || fid,\n      registrationStatus: RequestStatus.COMPLETED,\n      refreshToken: responseValue.refreshToken,\n      authToken: extractAuthTokenInfoFromResponse(responseValue.authToken)\n    };\n    return registeredInstallationEntry;\n  } else {\n    throw await getErrorFromResponse('Create Installation', response);\n  }\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { GenerateAuthTokenResponse } from '../interfaces/api-response';\nimport {\n  CompletedAuthToken,\n  RegisteredInstallationEntry\n} from '../interfaces/installation-entry';\nimport { PACKAGE_VERSION } from '../util/constants';\nimport {\n  extractAuthTokenInfoFromResponse,\n  getErrorFromResponse,\n  getHeadersWithAuth,\n  getInstallationsEndpoint,\n  retryIfServerError\n} from './common';\nimport {\n  FirebaseInstallationsImpl,\n  AppConfig\n} from '../interfaces/installation-impl';\n\nexport async function generateAuthTokenRequest(\n  { appConfig, heartbeatServiceProvider }: FirebaseInstallationsImpl,\n  installationEntry: RegisteredInstallationEntry\n): Promise<CompletedAuthToken> {\n  const endpoint = getGenerateAuthTokenEndpoint(appConfig, installationEntry);\n\n  const headers = getHeadersWithAuth(appConfig, installationEntry);\n\n  // If heartbeat service exists, add the heartbeat string to the header.\n  const heartbeatService = heartbeatServiceProvider.getImmediate({\n    optional: true\n  });\n  if (heartbeatService) {\n    const heartbeatsHeader = await heartbeatService.getHeartbeatsHeader();\n    if (heartbeatsHeader) {\n      headers.append('x-firebase-client', heartbeatsHeader);\n    }\n  }\n\n  const body = {\n    installation: {\n      sdkVersion: PACKAGE_VERSION,\n      appId: appConfig.appId\n    }\n  };\n\n  const request: RequestInit = {\n    method: 'POST',\n    headers,\n    body: JSON.stringify(body)\n  };\n\n  const response = await retryIfServerError(() => fetch(endpoint, request));\n  if (response.ok) {\n    const responseValue: GenerateAuthTokenResponse = await response.json();\n    const completedAuthToken: CompletedAuthToken =\n      extractAuthTokenInfoFromResponse(responseValue);\n    return completedAuthToken;\n  } else {\n    throw await getErrorFromResponse('Generate Auth Token', response);\n  }\n}\n\nfunction getGenerateAuthTokenEndpoint(\n  appConfig: AppConfig,\n  { fid }: RegisteredInstallationEntry\n): string {\n  return `${getInstallationsEndpoint(appConfig)}/${fid}/authTokens:generate`;\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { generateAuthTokenRequest } from '../functions/generate-auth-token-request';\nimport {\n  AppConfig,\n  FirebaseInstallationsImpl\n} from '../interfaces/installation-impl';\nimport {\n  AuthToken,\n  CompletedAuthToken,\n  InProgressAuthToken,\n  InstallationEntry,\n  RegisteredInstallationEntry,\n  RequestStatus\n} from '../interfaces/installation-entry';\nimport { PENDING_TIMEOUT_MS, TOKEN_EXPIRATION_BUFFER } from '../util/constants';\nimport { ERROR_FACTORY, ErrorCode, isServerError } from '../util/errors';\nimport { sleep } from '../util/sleep';\nimport { remove, set, update } from './idb-manager';\n\n/**\n * Returns a valid authentication token for the installation. Generates a new\n * token if one doesn't exist, is expired or about to expire.\n *\n * Should only be called if the Firebase Installation is registered.\n */\nexport async function refreshAuthToken(\n  installations: FirebaseInstallationsImpl,\n  forceRefresh = false\n): Promise<CompletedAuthToken> {\n  let tokenPromise: Promise<CompletedAuthToken> | undefined;\n  const entry = await update(installations.appConfig, oldEntry => {\n    if (!isEntryRegistered(oldEntry)) {\n      throw ERROR_FACTORY.create(ErrorCode.NOT_REGISTERED);\n    }\n\n    const oldAuthToken = oldEntry.authToken;\n    if (!forceRefresh && isAuthTokenValid(oldAuthToken)) {\n      // There is a valid token in the DB.\n      return oldEntry;\n    } else if (oldAuthToken.requestStatus === RequestStatus.IN_PROGRESS) {\n      // There already is a token request in progress.\n      tokenPromise = waitUntilAuthTokenRequest(installations, forceRefresh);\n      return oldEntry;\n    } else {\n      // No token or token expired.\n      if (!navigator.onLine) {\n        throw ERROR_FACTORY.create(ErrorCode.APP_OFFLINE);\n      }\n\n      const inProgressEntry = makeAuthTokenRequestInProgressEntry(oldEntry);\n      tokenPromise = fetchAuthTokenFromServer(installations, inProgressEntry);\n      return inProgressEntry;\n    }\n  });\n\n  const authToken = tokenPromise\n    ? await tokenPromise\n    : (entry.authToken as CompletedAuthToken);\n  return authToken;\n}\n\n/**\n * Call only if FID is registered and Auth Token request is in progress.\n *\n * Waits until the current pending request finishes. If the request times out,\n * tries once in this thread as well.\n */\nasync function waitUntilAuthTokenRequest(\n  installations: FirebaseInstallationsImpl,\n  forceRefresh: boolean\n): Promise<CompletedAuthToken> {\n  // Unfortunately, there is no way of reliably observing when a value in\n  // IndexedDB changes (yet, see https://github.com/WICG/indexed-db-observers),\n  // so we need to poll.\n\n  let entry = await updateAuthTokenRequest(installations.appConfig);\n  while (entry.authToken.requestStatus === RequestStatus.IN_PROGRESS) {\n    // generateAuthToken still in progress.\n    await sleep(100);\n\n    entry = await updateAuthTokenRequest(installations.appConfig);\n  }\n\n  const authToken = entry.authToken;\n  if (authToken.requestStatus === RequestStatus.NOT_STARTED) {\n    // The request timed out or failed in a different call. Try again.\n    return refreshAuthToken(installations, forceRefresh);\n  } else {\n    return authToken;\n  }\n}\n\n/**\n * Called only if there is a GenerateAuthToken request in progress.\n *\n * Updates the InstallationEntry in the DB based on the status of the\n * GenerateAuthToken request.\n *\n * Returns the updated InstallationEntry.\n */\nfunction updateAuthTokenRequest(\n  appConfig: AppConfig\n): Promise<RegisteredInstallationEntry> {\n  return update(appConfig, oldEntry => {\n    if (!isEntryRegistered(oldEntry)) {\n      throw ERROR_FACTORY.create(ErrorCode.NOT_REGISTERED);\n    }\n\n    const oldAuthToken = oldEntry.authToken;\n    if (hasAuthTokenRequestTimedOut(oldAuthToken)) {\n      return {\n        ...oldEntry,\n        authToken: { requestStatus: RequestStatus.NOT_STARTED }\n      };\n    }\n\n    return oldEntry;\n  });\n}\n\nasync function fetchAuthTokenFromServer(\n  installations: FirebaseInstallationsImpl,\n  installationEntry: RegisteredInstallationEntry\n): Promise<CompletedAuthToken> {\n  try {\n    const authToken = await generateAuthTokenRequest(\n      installations,\n      installationEntry\n    );\n    const updatedInstallationEntry: RegisteredInstallationEntry = {\n      ...installationEntry,\n      authToken\n    };\n    await set(installations.appConfig, updatedInstallationEntry);\n    return authToken;\n  } catch (e) {\n    if (\n      isServerError(e) &&\n      (e.customData.serverCode === 401 || e.customData.serverCode === 404)\n    ) {\n      // Server returned a \"FID not found\" or a \"Invalid authentication\" error.\n      // Generate a new ID next time.\n      await remove(installations.appConfig);\n    } else {\n      const updatedInstallationEntry: RegisteredInstallationEntry = {\n        ...installationEntry,\n        authToken: { requestStatus: RequestStatus.NOT_STARTED }\n      };\n      await set(installations.appConfig, updatedInstallationEntry);\n    }\n    throw e;\n  }\n}\n\nfunction isEntryRegistered(\n  installationEntry: InstallationEntry | undefined\n): installationEntry is RegisteredInstallationEntry {\n  return (\n    installationEntry !== undefined &&\n    installationEntry.registrationStatus === RequestStatus.COMPLETED\n  );\n}\n\nfunction isAuthTokenValid(authToken: AuthToken): boolean {\n  return (\n    authToken.requestStatus === RequestStatus.COMPLETED &&\n    !isAuthTokenExpired(authToken)\n  );\n}\n\nfunction isAuthTokenExpired(authToken: CompletedAuthToken): boolean {\n  const now = Date.now();\n  return (\n    now < authToken.creationTime ||\n    authToken.creationTime + authToken.expiresIn < now + TOKEN_EXPIRATION_BUFFER\n  );\n}\n\n/** Returns an updated InstallationEntry with an InProgressAuthToken. */\nfunction makeAuthTokenRequestInProgressEntry(\n  oldEntry: RegisteredInstallationEntry\n): RegisteredInstallationEntry {\n  const inProgressAuthToken: InProgressAuthToken = {\n    requestStatus: RequestStatus.IN_PROGRESS,\n    requestTime: Date.now()\n  };\n  return {\n    ...oldEntry,\n    authToken: inProgressAuthToken\n  };\n}\n\nfunction hasAuthTokenRequestTimedOut(authToken: AuthToken): boolean {\n  return (\n    authToken.requestStatus === RequestStatus.IN_PROGRESS &&\n    authToken.requestTime + PENDING_TIMEOUT_MS < Date.now()\n  );\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { getInstallationEntry } from '../helpers/get-installation-entry';\nimport { refreshAuthToken } from '../helpers/refresh-auth-token';\nimport { FirebaseInstallationsImpl } from '../interfaces/installation-impl';\nimport { Installations } from '../interfaces/public-types';\n\n/**\n * Returns a Firebase Installations auth token, identifying the current\n * Firebase Installation.\n * @param installations - The `Installations` instance.\n * @param forceRefresh - Force refresh regardless of token expiration.\n *\n * @public\n */\nexport async function getToken(\n  installations: Installations,\n  forceRefresh = false\n): Promise<string> {\n  const installationsImpl = installations as FirebaseInstallationsImpl;\n  await completeInstallationRegistration(installationsImpl);\n\n  // At this point we either have a Registered Installation in the DB, or we've\n  // already thrown an error.\n  const authToken = await refreshAuthToken(installationsImpl, forceRefresh);\n  return authToken.token;\n}\n\nasync function completeInstallationRegistration(\n  installations: FirebaseInstallationsImpl\n): Promise<void> {\n  const { registrationPromise } = await getInstallationEntry(installations);\n\n  if (registrationPromise) {\n    // A createInstallation request is in progress. Wait until it finishes.\n    await registrationPromise;\n  }\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { FirebaseApp, FirebaseOptions } from '@firebase/app';\nimport { FirebaseError } from '@firebase/util';\nimport { AppConfig } from '../interfaces/installation-impl';\nimport { ERROR_FACTORY, ErrorCode } from '../util/errors';\n\nexport function extractAppConfig(app: FirebaseApp): AppConfig {\n  if (!app || !app.options) {\n    throw getMissingValueError('App Configuration');\n  }\n\n  if (!app.name) {\n    throw getMissingValueError('App Name');\n  }\n\n  // Required app config keys\n  const configKeys: Array<keyof FirebaseOptions> = [\n    'projectId',\n    'apiKey',\n    'appId'\n  ];\n\n  for (const keyName of configKeys) {\n    if (!app.options[keyName]) {\n      throw getMissingValueError(keyName);\n    }\n  }\n\n  return {\n    appName: app.name,\n    projectId: app.options.projectId!,\n    apiKey: app.options.apiKey!,\n    appId: app.options.appId!\n  };\n}\n\nfunction getMissingValueError(valueName: string): FirebaseError {\n  return ERROR_FACTORY.create(ErrorCode.MISSING_APP_CONFIG_VALUES, {\n    valueName\n  });\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { _registerComponent, _getProvider } from '@firebase/app';\nimport {\n  Component,\n  ComponentType,\n  InstanceFactory,\n  ComponentContainer\n} from '@firebase/component';\nimport { getId, getToken } from '../api/index';\nimport { _FirebaseInstallationsInternal } from '../interfaces/public-types';\nimport { FirebaseInstallationsImpl } from '../interfaces/installation-impl';\nimport { extractAppConfig } from '../helpers/extract-app-config';\n\nconst INSTALLATIONS_NAME = 'installations';\nconst INSTALLATIONS_NAME_INTERNAL = 'installations-internal';\n\nconst publicFactory: InstanceFactory<'installations'> = (\n  container: ComponentContainer\n) => {\n  const app = container.getProvider('app').getImmediate();\n  // Throws if app isn't configured properly.\n  const appConfig = extractAppConfig(app);\n  const heartbeatServiceProvider = _getProvider(app, 'heartbeat');\n\n  const installationsImpl: FirebaseInstallationsImpl = {\n    app,\n    appConfig,\n    heartbeatServiceProvider,\n    _delete: () => Promise.resolve()\n  };\n  return installationsImpl;\n};\n\nconst internalFactory: InstanceFactory<'installations-internal'> = (\n  container: ComponentContainer\n) => {\n  const app = container.getProvider('app').getImmediate();\n  // Internal FIS instance relies on public FIS instance.\n  const installations = _getProvider(app, INSTALLATIONS_NAME).getImmediate();\n\n  const installationsInternal: _FirebaseInstallationsInternal = {\n    getId: () => getId(installations),\n    getToken: (forceRefresh?: boolean) => getToken(installations, forceRefresh)\n  };\n  return installationsInternal;\n};\n\nexport function registerInstallations(): void {\n  _registerComponent(\n    new Component(INSTALLATIONS_NAME, publicFactory, ComponentType.PUBLIC)\n  );\n  _registerComponent(\n    new Component(\n      INSTALLATIONS_NAME_INTERNAL,\n      internalFactory,\n      ComponentType.PRIVATE\n    )\n  );\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { getInstallationEntry } from '../helpers/get-installation-entry';\nimport { refreshAuthToken } from '../helpers/refresh-auth-token';\nimport { FirebaseInstallationsImpl } from '../interfaces/installation-impl';\nimport { Installations } from '../interfaces/public-types';\n\n/**\n * Creates a Firebase Installation if there isn't one for the app and\n * returns the Installation ID.\n * @param installations - The `Installations` instance.\n *\n * @public\n */\nexport async function getId(installations: Installations): Promise<string> {\n  const installationsImpl = installations as FirebaseInstallationsImpl;\n  const { installationEntry, registrationPromise } = await getInstallationEntry(\n    installationsImpl\n  );\n\n  if (registrationPromise) {\n    registrationPromise.catch(console.error);\n  } else {\n    // If the installation is already registered, update the authentication\n    // token if needed.\n    refreshAuthToken(installationsImpl).catch(console.error);\n  }\n\n  return installationEntry.fid;\n}\n", "/**\n * The Firebase Installations Web SDK.\n * This SDK does not work in a Node.js environment.\n *\n * @packageDocumentation\n */\n\n/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { registerInstallations } from './functions/config';\nimport { registerVersion } from '@firebase/app';\nimport { name, version } from '../package.json';\n\nexport * from './api';\nexport * from './interfaces/public-types';\n\nregisterInstallations();\nregisterVersion(name, version);\n// BUILD_TARGET will be replaced by values like esm, cjs, etc during the compilation\nregisterVersion(name, version, '__BUILD_TARGET__');\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { CustomSignals, FetchResponse } from '../public_types';\n\n/**\n * Defines a client, as in https://en.wikipedia.org/wiki/Client%E2%80%93server_model, for the\n * Remote Config server (https://firebase.google.com/docs/reference/remote-config/rest).\n *\n * <p>Abstracts throttle, response cache and network implementation details.\n *\n * <p>Modeled after the native {@link GlobalFetch} interface, which is relatively modern and\n * convenient, but simplified for Remote Config's use case.\n *\n * Disambiguation: {@link GlobalFetch} interface and the Remote Config service define \"fetch\"\n * methods. The RestClient uses the former to make HTTP calls. This interface abstracts the latter.\n */\nexport interface RemoteConfigFetchClient {\n  /**\n   * @throws if response status is not 200 or 304.\n   */\n  fetch(request: FetchRequest): Promise<FetchResponse>;\n}\n\n/**\n * Shims a minimal AbortSignal.\n *\n * <p>AbortController's AbortSignal conveniently decouples fetch timeout logic from other aspects\n * of networking, such as retries. Firebase doesn't use AbortController enough to justify a\n * polyfill recommendation, like we do with the Fetch API, but this minimal shim can easily be\n * swapped out if/when we do.\n */\nexport class RemoteConfigAbortSignal {\n  listeners: Array<() => void> = [];\n  addEventListener(listener: () => void): void {\n    this.listeners.push(listener);\n  }\n  abort(): void {\n    this.listeners.forEach(listener => listener());\n  }\n}\n\n/**\n * Defines per-request inputs for the Remote Config fetch request.\n *\n * <p>Modeled after the native {@link Request} interface, but simplified for Remote Config's\n * use case.\n */\nexport interface FetchRequest {\n  /**\n   * Uses cached config if it is younger than this age.\n   *\n   * <p>Required because it's defined by settings, which always have a value.\n   *\n   * <p>Comparable to passing `headers = { 'Cache-Control': max-age <maxAge> }` to the native\n   * Fetch API.\n   */\n  cacheMaxAgeMillis: number;\n\n  /**\n   * An event bus for the signal to abort a request.\n   *\n   * <p>Required because all requests should be abortable.\n   *\n   * <p>Comparable to the native\n   * Fetch API's \"signal\" field on its request configuration object\n   * https://fetch.spec.whatwg.org/#dom-requestinit-signal.\n   *\n   * <p>Disambiguation: Remote Config commonly refers to API inputs as\n   * \"signals\". See the private ConfigFetchRequestBody interface for those:\n   * http://google3/firebase/remote_config/web/src/core/rest_client.ts?l=14&rcl=255515243.\n   */\n  signal: RemoteConfigAbortSignal;\n\n  /**\n   * The ETag header value from the last response.\n   *\n   * <p>Optional in case this is the first request.\n   *\n   * <p>Comparable to passing `headers = { 'If-None-Match': <eTag> }` to the native Fetch API.\n   */\n  eTag?: string;\n\n  /** The custom signals stored for the app instance.\n   *\n   * <p>Optional in case no custom signals are set for the instance.\n   */\n  customSignals?: CustomSignals;\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport const RC_COMPONENT_NAME = 'remote-config';\nexport const RC_CUSTOM_SIGNAL_MAX_ALLOWED_SIGNALS = 100;\nexport const RC_CUSTOM_SIGNAL_KEY_MAX_LENGTH = 250;\nexport const RC_CUSTOM_SIGNAL_VALUE_MAX_LENGTH = 500;\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { ErrorFactory, FirebaseError } from '@firebase/util';\n\nexport const enum ErrorCode {\n  ALREADY_INITIALIZED = 'already-initialized',\n  REGISTRATION_WINDOW = 'registration-window',\n  REGISTRATION_PROJECT_ID = 'registration-project-id',\n  REGISTRATION_API_KEY = 'registration-api-key',\n  REGISTRATION_APP_ID = 'registration-app-id',\n  STORAGE_OPEN = 'storage-open',\n  STORAGE_GET = 'storage-get',\n  STORAGE_SET = 'storage-set',\n  STORAGE_DELETE = 'storage-delete',\n  FETCH_NETWORK = 'fetch-client-network',\n  FETCH_TIMEOUT = 'fetch-timeout',\n  FETCH_THROTTLE = 'fetch-throttle',\n  FETCH_PARSE = 'fetch-client-parse',\n  FETCH_STATUS = 'fetch-status',\n  INDEXED_DB_UNAVAILABLE = 'indexed-db-unavailable',\n  CUSTOM_SIGNAL_MAX_ALLOWED_SIGNALS = 'custom-signal-max-allowed-signals'\n}\n\nconst ERROR_DESCRIPTION_MAP: { readonly [key in ErrorCode]: string } = {\n  [ErrorCode.ALREADY_INITIALIZED]: 'Remote Config already initialized',\n  [ErrorCode.REGISTRATION_WINDOW]:\n    'Undefined window object. This SDK only supports usage in a browser environment.',\n  [ErrorCode.REGISTRATION_PROJECT_ID]:\n    'Undefined project identifier. Check Firebase app initialization.',\n  [ErrorCode.REGISTRATION_API_KEY]:\n    'Undefined API key. Check Firebase app initialization.',\n  [ErrorCode.REGISTRATION_APP_ID]:\n    'Undefined app identifier. Check Firebase app initialization.',\n  [ErrorCode.STORAGE_OPEN]:\n    'Error thrown when opening storage. Original error: {$originalErrorMessage}.',\n  [ErrorCode.STORAGE_GET]:\n    'Error thrown when reading from storage. Original error: {$originalErrorMessage}.',\n  [ErrorCode.STORAGE_SET]:\n    'Error thrown when writing to storage. Original error: {$originalErrorMessage}.',\n  [ErrorCode.STORAGE_DELETE]:\n    'Error thrown when deleting from storage. Original error: {$originalErrorMessage}.',\n  [ErrorCode.FETCH_NETWORK]:\n    'Fetch client failed to connect to a network. Check Internet connection.' +\n    ' Original error: {$originalErrorMessage}.',\n  [ErrorCode.FETCH_TIMEOUT]:\n    'The config fetch request timed out. ' +\n    ' Configure timeout using \"fetchTimeoutMillis\" SDK setting.',\n  [ErrorCode.FETCH_THROTTLE]:\n    'The config fetch request timed out while in an exponential backoff state.' +\n    ' Configure timeout using \"fetchTimeoutMillis\" SDK setting.' +\n    ' Unix timestamp in milliseconds when fetch request throttling ends: {$throttleEndTimeMillis}.',\n  [ErrorCode.FETCH_PARSE]:\n    'Fetch client could not parse response.' +\n    ' Original error: {$originalErrorMessage}.',\n  [ErrorCode.FETCH_STATUS]:\n    'Fetch server returned an HTTP error status. HTTP status: {$httpStatus}.',\n  [ErrorCode.INDEXED_DB_UNAVAILABLE]:\n    'Indexed DB is not supported by current browser',\n  [ErrorCode.CUSTOM_SIGNAL_MAX_ALLOWED_SIGNALS]:\n    'Setting more than {$maxSignals} custom signals is not supported.'\n};\n\n// Note this is effectively a type system binding a code to params. This approach overlaps with the\n// role of TS interfaces, but works well for a few reasons:\n// 1) JS is unaware of TS interfaces, eg we can't test for interface implementation in JS\n// 2) callers should have access to a human-readable summary of the error and this interpolates\n//    params into an error message;\n// 3) callers should be able to programmatically access data associated with an error, which\n//    ErrorData provides.\ninterface ErrorParams {\n  [ErrorCode.STORAGE_OPEN]: { originalErrorMessage: string | undefined };\n  [ErrorCode.STORAGE_GET]: { originalErrorMessage: string | undefined };\n  [ErrorCode.STORAGE_SET]: { originalErrorMessage: string | undefined };\n  [ErrorCode.STORAGE_DELETE]: { originalErrorMessage: string | undefined };\n  [ErrorCode.FETCH_NETWORK]: { originalErrorMessage: string };\n  [ErrorCode.FETCH_THROTTLE]: { throttleEndTimeMillis: number };\n  [ErrorCode.FETCH_PARSE]: { originalErrorMessage: string };\n  [ErrorCode.FETCH_STATUS]: { httpStatus: number };\n  [ErrorCode.CUSTOM_SIGNAL_MAX_ALLOWED_SIGNALS]: { maxSignals: number };\n}\n\nexport const ERROR_FACTORY = new ErrorFactory<ErrorCode, ErrorParams>(\n  'remoteconfig' /* service */,\n  'Remote Config' /* service name */,\n  ERROR_DESCRIPTION_MAP\n);\n\n// Note how this is like typeof/instanceof, but for ErrorCode.\nexport function hasErrorCode(e: Error, errorCode: ErrorCode): boolean {\n  return e instanceof FirebaseError && e.code.indexOf(errorCode) !== -1;\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Value as ValueType, ValueSource } from '@firebase/remote-config-types';\n\nconst DEFAULT_VALUE_FOR_BOOLEAN = false;\nconst DEFAULT_VALUE_FOR_STRING = '';\nconst DEFAULT_VALUE_FOR_NUMBER = 0;\n\nconst BOOLEAN_TRUTHY_VALUES = ['1', 'true', 't', 'yes', 'y', 'on'];\n\nexport class Value implements ValueType {\n  constructor(\n    private readonly _source: ValueSource,\n    private readonly _value: string = DEFAULT_VALUE_FOR_STRING\n  ) {}\n\n  asString(): string {\n    return this._value;\n  }\n\n  asBoolean(): boolean {\n    if (this._source === 'static') {\n      return DEFAULT_VALUE_FOR_BOOLEAN;\n    }\n    return BOOLEAN_TRUTHY_VALUES.indexOf(this._value.toLowerCase()) >= 0;\n  }\n\n  asNumber(): number {\n    if (this._source === 'static') {\n      return DEFAULT_VALUE_FOR_NUMBER;\n    }\n    let num = Number(this._value);\n    if (isNaN(num)) {\n      num = DEFAULT_VALUE_FOR_NUMBER;\n    }\n    return num;\n  }\n\n  getSource(): ValueSource {\n    return this._source;\n  }\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { _getProvider, FirebaseApp, getApp } from '@firebase/app';\nimport { deepEqual, getModularInstance } from '@firebase/util';\nimport {\n  CustomSignals,\n  LogLevel as RemoteConfigLogLevel,\n  RemoteConfig,\n  Value,\n  RemoteConfigOptions\n} from './public_types';\nimport { RemoteConfigAbortSignal } from './client/remote_config_fetch_client';\nimport {\n  RC_COMPONENT_NAME,\n  RC_CUSTOM_SIGNAL_KEY_MAX_LENGTH,\n  RC_CUSTOM_SIGNAL_VALUE_MAX_LENGTH\n} from './constants';\nimport { ERROR_FACTORY, ErrorCode, hasErrorCode } from './errors';\nimport { RemoteConfig as RemoteConfigImpl } from './remote_config';\nimport { Value as ValueImpl } from './value';\nimport { LogLevel as FirebaseLogLevel } from '@firebase/logger';\n\n/**\n *\n * @param app - The {@link @firebase/app#FirebaseApp} instance.\n * @param options - Optional. The {@link RemoteConfigOptions} with which to instantiate the\n *     Remote Config instance.\n * @returns A {@link RemoteConfig} instance.\n *\n * @public\n */\nexport function getRemoteConfig(\n  app: FirebaseApp = getApp(),\n  options: RemoteConfigOptions = {}\n): RemoteConfig {\n  app = getModularInstance(app);\n  const rcProvider = _getProvider(app, RC_COMPONENT_NAME);\n  if (rcProvider.isInitialized()) {\n    const initialOptions = rcProvider.getOptions() as RemoteConfigOptions;\n    if (deepEqual(initialOptions, options)) {\n      return rcProvider.getImmediate();\n    }\n    throw ERROR_FACTORY.create(ErrorCode.ALREADY_INITIALIZED);\n  }\n  rcProvider.initialize({ options });\n  const rc = rcProvider.getImmediate() as RemoteConfigImpl;\n\n  if (options.initialFetchResponse) {\n    // We use these initial writes as the initialization promise since they will hydrate the same\n    // fields that `storageCache.loadFromStorage` would set.\n    rc._initializePromise = Promise.all([\n      rc._storage.setLastSuccessfulFetchResponse(options.initialFetchResponse),\n      rc._storage.setActiveConfigEtag(options.initialFetchResponse?.eTag || ''),\n      rc._storageCache.setLastSuccessfulFetchTimestampMillis(Date.now()),\n      rc._storageCache.setLastFetchStatus('success'),\n      rc._storageCache.setActiveConfig(\n        options.initialFetchResponse?.config || {}\n      )\n    ]).then();\n    // The `storageCache` methods above set their in-memory fields synchronously, so it's\n    // safe to declare our initialization complete at this point.\n    rc._isInitializationComplete = true;\n  }\n\n  return rc;\n}\n\n/**\n * Makes the last fetched config available to the getters.\n * @param remoteConfig - The {@link RemoteConfig} instance.\n * @returns A `Promise` which resolves to true if the current call activated the fetched configs.\n * If the fetched configs were already activated, the `Promise` will resolve to false.\n *\n * @public\n */\nexport async function activate(remoteConfig: RemoteConfig): Promise<boolean> {\n  const rc = getModularInstance(remoteConfig) as RemoteConfigImpl;\n  const [lastSuccessfulFetchResponse, activeConfigEtag] = await Promise.all([\n    rc._storage.getLastSuccessfulFetchResponse(),\n    rc._storage.getActiveConfigEtag()\n  ]);\n  if (\n    !lastSuccessfulFetchResponse ||\n    !lastSuccessfulFetchResponse.config ||\n    !lastSuccessfulFetchResponse.eTag ||\n    lastSuccessfulFetchResponse.eTag === activeConfigEtag\n  ) {\n    // Either there is no successful fetched config, or is the same as current active\n    // config.\n    return false;\n  }\n  await Promise.all([\n    rc._storageCache.setActiveConfig(lastSuccessfulFetchResponse.config),\n    rc._storage.setActiveConfigEtag(lastSuccessfulFetchResponse.eTag)\n  ]);\n  return true;\n}\n\n/**\n * Ensures the last activated config are available to the getters.\n * @param remoteConfig - The {@link RemoteConfig} instance.\n *\n * @returns A `Promise` that resolves when the last activated config is available to the getters.\n * @public\n */\nexport function ensureInitialized(remoteConfig: RemoteConfig): Promise<void> {\n  const rc = getModularInstance(remoteConfig) as RemoteConfigImpl;\n  if (!rc._initializePromise) {\n    rc._initializePromise = rc._storageCache.loadFromStorage().then(() => {\n      rc._isInitializationComplete = true;\n    });\n  }\n  return rc._initializePromise;\n}\n\n/**\n * Fetches and caches configuration from the Remote Config service.\n * @param remoteConfig - The {@link RemoteConfig} instance.\n * @public\n */\nexport async function fetchConfig(remoteConfig: RemoteConfig): Promise<void> {\n  const rc = getModularInstance(remoteConfig) as RemoteConfigImpl;\n  // Aborts the request after the given timeout, causing the fetch call to\n  // reject with an `AbortError`.\n  //\n  // <p>Aborting after the request completes is a no-op, so we don't need a\n  // corresponding `clearTimeout`.\n  //\n  // Locating abort logic here because:\n  // * it uses a developer setting (timeout)\n  // * it applies to all retries (like curl's max-time arg)\n  // * it is consistent with the Fetch API's signal input\n  const abortSignal = new RemoteConfigAbortSignal();\n\n  setTimeout(async () => {\n    // Note a very low delay, eg < 10ms, can elapse before listeners are initialized.\n    abortSignal.abort();\n  }, rc.settings.fetchTimeoutMillis);\n\n  const customSignals = rc._storageCache.getCustomSignals();\n  if (customSignals) {\n    rc._logger.debug(\n      `Fetching config with custom signals: ${JSON.stringify(customSignals)}`\n    );\n  }\n  // Catches *all* errors thrown by client so status can be set consistently.\n  try {\n    await rc._client.fetch({\n      cacheMaxAgeMillis: rc.settings.minimumFetchIntervalMillis,\n      signal: abortSignal,\n      customSignals\n    });\n\n    await rc._storageCache.setLastFetchStatus('success');\n  } catch (e) {\n    const lastFetchStatus = hasErrorCode(e as Error, ErrorCode.FETCH_THROTTLE)\n      ? 'throttle'\n      : 'failure';\n    await rc._storageCache.setLastFetchStatus(lastFetchStatus);\n    throw e;\n  }\n}\n\n/**\n * Gets all config.\n *\n * @param remoteConfig - The {@link RemoteConfig} instance.\n * @returns All config.\n *\n * @public\n */\nexport function getAll(remoteConfig: RemoteConfig): Record<string, Value> {\n  const rc = getModularInstance(remoteConfig) as RemoteConfigImpl;\n  return getAllKeys(\n    rc._storageCache.getActiveConfig(),\n    rc.defaultConfig\n  ).reduce((allConfigs, key) => {\n    allConfigs[key] = getValue(remoteConfig, key);\n    return allConfigs;\n  }, {} as Record<string, Value>);\n}\n\n/**\n * Gets the value for the given key as a boolean.\n *\n * Convenience method for calling <code>remoteConfig.getValue(key).asBoolean()</code>.\n *\n * @param remoteConfig - The {@link RemoteConfig} instance.\n * @param key - The name of the parameter.\n *\n * @returns The value for the given key as a boolean.\n * @public\n */\nexport function getBoolean(remoteConfig: RemoteConfig, key: string): boolean {\n  return getValue(getModularInstance(remoteConfig), key).asBoolean();\n}\n\n/**\n * Gets the value for the given key as a number.\n *\n * Convenience method for calling <code>remoteConfig.getValue(key).asNumber()</code>.\n *\n * @param remoteConfig - The {@link RemoteConfig} instance.\n * @param key - The name of the parameter.\n *\n * @returns The value for the given key as a number.\n *\n * @public\n */\nexport function getNumber(remoteConfig: RemoteConfig, key: string): number {\n  return getValue(getModularInstance(remoteConfig), key).asNumber();\n}\n\n/**\n * Gets the value for the given key as a string.\n * Convenience method for calling <code>remoteConfig.getValue(key).asString()</code>.\n *\n * @param remoteConfig - The {@link RemoteConfig} instance.\n * @param key - The name of the parameter.\n *\n * @returns The value for the given key as a string.\n *\n * @public\n */\nexport function getString(remoteConfig: RemoteConfig, key: string): string {\n  return getValue(getModularInstance(remoteConfig), key).asString();\n}\n\n/**\n * Gets the {@link Value} for the given key.\n *\n * @param remoteConfig - The {@link RemoteConfig} instance.\n * @param key - The name of the parameter.\n *\n * @returns The value for the given key.\n *\n * @public\n */\nexport function getValue(remoteConfig: RemoteConfig, key: string): Value {\n  const rc = getModularInstance(remoteConfig) as RemoteConfigImpl;\n  if (!rc._isInitializationComplete) {\n    rc._logger.debug(\n      `A value was requested for key \"${key}\" before SDK initialization completed.` +\n        ' Await on ensureInitialized if the intent was to get a previously activated value.'\n    );\n  }\n  const activeConfig = rc._storageCache.getActiveConfig();\n  if (activeConfig && activeConfig[key] !== undefined) {\n    return new ValueImpl('remote', activeConfig[key]);\n  } else if (rc.defaultConfig && rc.defaultConfig[key] !== undefined) {\n    return new ValueImpl('default', String(rc.defaultConfig[key]));\n  }\n  rc._logger.debug(\n    `Returning static value for key \"${key}\".` +\n      ' Define a default or remote value if this is unintentional.'\n  );\n  return new ValueImpl('static');\n}\n\n/**\n * Defines the log level to use.\n *\n * @param remoteConfig - The {@link RemoteConfig} instance.\n * @param logLevel - The log level to set.\n *\n * @public\n */\nexport function setLogLevel(\n  remoteConfig: RemoteConfig,\n  logLevel: RemoteConfigLogLevel\n): void {\n  const rc = getModularInstance(remoteConfig) as RemoteConfigImpl;\n  switch (logLevel) {\n    case 'debug':\n      rc._logger.logLevel = FirebaseLogLevel.DEBUG;\n      break;\n    case 'silent':\n      rc._logger.logLevel = FirebaseLogLevel.SILENT;\n      break;\n    default:\n      rc._logger.logLevel = FirebaseLogLevel.ERROR;\n  }\n}\n\n/**\n * Dedupes and returns an array of all the keys of the received objects.\n */\nfunction getAllKeys(obj1: {} = {}, obj2: {} = {}): string[] {\n  return Object.keys({ ...obj1, ...obj2 });\n}\n\n/**\n * Sets the custom signals for the app instance.\n *\n * @param remoteConfig - The {@link RemoteConfig} instance.\n * @param customSignals - Map (key, value) of the custom signals to be set for the app instance. If\n * a key already exists, the value is overwritten. Setting the value of a custom signal to null\n * unsets the signal. The signals will be persisted locally on the client.\n *\n * @public\n */\nexport async function setCustomSignals(\n  remoteConfig: RemoteConfig,\n  customSignals: CustomSignals\n): Promise<void> {\n  const rc = getModularInstance(remoteConfig) as RemoteConfigImpl;\n  if (Object.keys(customSignals).length === 0) {\n    return;\n  }\n\n  // eslint-disable-next-line guard-for-in\n  for (const key in customSignals) {\n    if (key.length > RC_CUSTOM_SIGNAL_KEY_MAX_LENGTH) {\n      rc._logger.error(\n        `Custom signal key ${key} is too long, max allowed length is ${RC_CUSTOM_SIGNAL_KEY_MAX_LENGTH}.`\n      );\n      return;\n    }\n    const value = customSignals[key];\n    if (\n      typeof value === 'string' &&\n      value.length > RC_CUSTOM_SIGNAL_VALUE_MAX_LENGTH\n    ) {\n      rc._logger.error(\n        `Value supplied for custom signal ${key} is too long, max allowed length is ${RC_CUSTOM_SIGNAL_VALUE_MAX_LENGTH}.`\n      );\n      return;\n    }\n  }\n\n  try {\n    await rc._storageCache.setCustomSignals(customSignals);\n  } catch (error) {\n    rc._logger.error(\n      `Error encountered while setting custom signals: ${error}`\n    );\n  }\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { StorageCache } from '../storage/storage_cache';\nimport { FetchResponse } from '../public_types';\nimport {\n  RemoteConfigFetchClient,\n  FetchRequest\n} from './remote_config_fetch_client';\nimport { Storage } from '../storage/storage';\nimport { Logger } from '@firebase/logger';\n\n/**\n * Implements the {@link RemoteConfigClient} abstraction with success response caching.\n *\n * <p>Comparable to the browser's Cache API for responses, but the Cache API requires a Service\n * Worker, which requires HTTPS, which would significantly complicate SDK installation. Also, the\n * Cache API doesn't support matching entries by time.\n */\nexport class CachingClient implements RemoteConfigFetchClient {\n  constructor(\n    private readonly client: RemoteConfigFetchClient,\n    private readonly storage: Storage,\n    private readonly storageCache: StorageCache,\n    private readonly logger: Logger\n  ) {}\n\n  /**\n   * Returns true if the age of the cached fetched configs is less than or equal to\n   * {@link Settings#minimumFetchIntervalInSeconds}.\n   *\n   * <p>This is comparable to passing `headers = { 'Cache-Control': max-age <maxAge> }` to the\n   * native Fetch API.\n   *\n   * <p>Visible for testing.\n   */\n  isCachedDataFresh(\n    cacheMaxAgeMillis: number,\n    lastSuccessfulFetchTimestampMillis: number | undefined\n  ): boolean {\n    // Cache can only be fresh if it's populated.\n    if (!lastSuccessfulFetchTimestampMillis) {\n      this.logger.debug('Config fetch cache check. Cache unpopulated.');\n      return false;\n    }\n\n    // Calculates age of cache entry.\n    const cacheAgeMillis = Date.now() - lastSuccessfulFetchTimestampMillis;\n\n    const isCachedDataFresh = cacheAgeMillis <= cacheMaxAgeMillis;\n\n    this.logger.debug(\n      'Config fetch cache check.' +\n        ` Cache age millis: ${cacheAgeMillis}.` +\n        ` Cache max age millis (minimumFetchIntervalMillis setting): ${cacheMaxAgeMillis}.` +\n        ` Is cache hit: ${isCachedDataFresh}.`\n    );\n\n    return isCachedDataFresh;\n  }\n\n  async fetch(request: FetchRequest): Promise<FetchResponse> {\n    // Reads from persisted storage to avoid cache miss if callers don't wait on initialization.\n    const [lastSuccessfulFetchTimestampMillis, lastSuccessfulFetchResponse] =\n      await Promise.all([\n        this.storage.getLastSuccessfulFetchTimestampMillis(),\n        this.storage.getLastSuccessfulFetchResponse()\n      ]);\n\n    // Exits early on cache hit.\n    if (\n      lastSuccessfulFetchResponse &&\n      this.isCachedDataFresh(\n        request.cacheMaxAgeMillis,\n        lastSuccessfulFetchTimestampMillis\n      )\n    ) {\n      return lastSuccessfulFetchResponse;\n    }\n\n    // Deviates from pure decorator by not honoring a passed ETag since we don't have a public API\n    // that allows the caller to pass an ETag.\n    request.eTag =\n      lastSuccessfulFetchResponse && lastSuccessfulFetchResponse.eTag;\n\n    // Falls back to service on cache miss.\n    const response = await this.client.fetch(request);\n\n    // Fetch throws for non-success responses, so success is guaranteed here.\n\n    const storageOperations = [\n      // Uses write-through cache for consistency with synchronous public API.\n      this.storageCache.setLastSuccessfulFetchTimestampMillis(Date.now())\n    ];\n\n    if (response.status === 200) {\n      // Caches response only if it has changed, ie non-304 responses.\n      storageOperations.push(\n        this.storage.setLastSuccessfulFetchResponse(response)\n      );\n    }\n\n    await Promise.all(storageOperations);\n\n    return response;\n  }\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * Attempts to get the most accurate browser language setting.\n *\n * <p>Adapted from getUserLanguage in packages/auth/src/utils.js for TypeScript.\n *\n * <p>Defers default language specification to server logic for consistency.\n *\n * @param navigatorLanguage Enables tests to override read-only {@link NavigatorLanguage}.\n */\nexport function getUserLanguage(\n  navigatorLanguage: NavigatorLanguage = navigator\n): string {\n  return (\n    // Most reliable, but only supported in Chrome/Firefox.\n    (navigatorLanguage.languages && navigatorLanguage.languages[0]) ||\n    // Supported in most browsers, but returns the language of the browser\n    // UI, not the language set in browser settings.\n    navigatorLanguage.language\n    // Polyfill otherwise.\n  );\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  CustomSignals,\n  FetchResponse,\n  FirebaseRemoteConfigObject\n} from '../public_types';\nimport {\n  RemoteConfigFetchClient,\n  FetchRequest\n} from './remote_config_fetch_client';\nimport { ERROR_FACTORY, ErrorCode } from '../errors';\nimport { getUserLanguage } from '../language';\nimport { _FirebaseInstallationsInternal } from '@firebase/installations';\n\n/**\n * Defines request body parameters required to call the fetch API:\n * https://firebase.google.com/docs/reference/remote-config/rest\n *\n * <p>Not exported because this file encapsulates REST API specifics.\n *\n * <p>Not passing User Properties because Analytics' source of truth on Web is server-side.\n */\ninterface FetchRequestBody {\n  // Disables camelcase linting for request body params.\n  /* eslint-disable camelcase*/\n  sdk_version: string;\n  app_instance_id: string;\n  app_instance_id_token: string;\n  app_id: string;\n  language_code: string;\n  custom_signals?: CustomSignals;\n  /* eslint-enable camelcase */\n}\n\n/**\n * Implements the Client abstraction for the Remote Config REST API.\n */\nexport class RestClient implements RemoteConfigFetchClient {\n  constructor(\n    private readonly firebaseInstallations: _FirebaseInstallationsInternal,\n    private readonly sdkVersion: string,\n    private readonly namespace: string,\n    private readonly projectId: string,\n    private readonly apiKey: string,\n    private readonly appId: string\n  ) {}\n\n  /**\n   * Fetches from the Remote Config REST API.\n   *\n   * @throws a {@link ErrorCode.FETCH_NETWORK} error if {@link GlobalFetch#fetch} can't\n   * connect to the network.\n   * @throws a {@link ErrorCode.FETCH_PARSE} error if {@link Response#json} can't parse the\n   * fetch response.\n   * @throws a {@link ErrorCode.FETCH_STATUS} error if the service returns an HTTP error status.\n   */\n  async fetch(request: FetchRequest): Promise<FetchResponse> {\n    const [installationId, installationToken] = await Promise.all([\n      this.firebaseInstallations.getId(),\n      this.firebaseInstallations.getToken()\n    ]);\n\n    const urlBase =\n      window.FIREBASE_REMOTE_CONFIG_URL_BASE ||\n      'https://firebaseremoteconfig.googleapis.com';\n\n    const url = `${urlBase}/v1/projects/${this.projectId}/namespaces/${this.namespace}:fetch?key=${this.apiKey}`;\n\n    const headers = {\n      'Content-Type': 'application/json',\n      'Content-Encoding': 'gzip',\n      // Deviates from pure decorator by not passing max-age header since we don't currently have\n      // service behavior using that header.\n      'If-None-Match': request.eTag || '*'\n    };\n\n    const requestBody: FetchRequestBody = {\n      /* eslint-disable camelcase */\n      sdk_version: this.sdkVersion,\n      app_instance_id: installationId,\n      app_instance_id_token: installationToken,\n      app_id: this.appId,\n      language_code: getUserLanguage(),\n      custom_signals: request.customSignals\n      /* eslint-enable camelcase */\n    };\n\n    const options = {\n      method: 'POST',\n      headers,\n      body: JSON.stringify(requestBody)\n    };\n\n    // This logic isn't REST-specific, but shimming abort logic isn't worth another decorator.\n    const fetchPromise = fetch(url, options);\n    const timeoutPromise = new Promise((_resolve, reject) => {\n      // Maps async event listener to Promise API.\n      request.signal.addEventListener(() => {\n        // Emulates https://heycam.github.io/webidl/#aborterror\n        const error = new Error('The operation was aborted.');\n        error.name = 'AbortError';\n        reject(error);\n      });\n    });\n\n    let response;\n    try {\n      await Promise.race([fetchPromise, timeoutPromise]);\n      response = await fetchPromise;\n    } catch (originalError) {\n      let errorCode = ErrorCode.FETCH_NETWORK;\n      if ((originalError as Error)?.name === 'AbortError') {\n        errorCode = ErrorCode.FETCH_TIMEOUT;\n      }\n      throw ERROR_FACTORY.create(errorCode, {\n        originalErrorMessage: (originalError as Error)?.message\n      });\n    }\n\n    let status = response.status;\n\n    // Normalizes nullable header to optional.\n    const responseEtag = response.headers.get('ETag') || undefined;\n\n    let config: FirebaseRemoteConfigObject | undefined;\n    let state: string | undefined;\n\n    // JSON parsing throws SyntaxError if the response body isn't a JSON string.\n    // Requesting application/json and checking for a 200 ensures there's JSON data.\n    if (response.status === 200) {\n      let responseBody;\n      try {\n        responseBody = await response.json();\n      } catch (originalError) {\n        throw ERROR_FACTORY.create(ErrorCode.FETCH_PARSE, {\n          originalErrorMessage: (originalError as Error)?.message\n        });\n      }\n      config = responseBody['entries'];\n      state = responseBody['state'];\n    }\n\n    // Normalizes based on legacy state.\n    if (state === 'INSTANCE_STATE_UNSPECIFIED') {\n      status = 500;\n    } else if (state === 'NO_CHANGE') {\n      status = 304;\n    } else if (state === 'NO_TEMPLATE' || state === 'EMPTY_CONFIG') {\n      // These cases can be fixed remotely, so normalize to safe value.\n      config = {};\n    }\n\n    // Normalize to exception-based control flow for non-success cases.\n    // Encapsulates HTTP specifics in this class as much as possible. Status is still the best for\n    // differentiating success states (200 from 304; the state body param is undefined in a\n    // standard 304).\n    if (status !== 304 && status !== 200) {\n      throw ERROR_FACTORY.create(ErrorCode.FETCH_STATUS, {\n        httpStatus: status\n      });\n    }\n\n    return { status, eTag: responseEtag, config };\n  }\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { FetchResponse } from '../public_types';\nimport {\n  RemoteConfigAbortSignal,\n  RemoteConfigFetchClient,\n  FetchRequest\n} from './remote_config_fetch_client';\nimport { ThrottleMetadata, Storage } from '../storage/storage';\nimport { ErrorCode, ERROR_FACTORY } from '../errors';\nimport { FirebaseError, calculateBackoffMillis } from '@firebase/util';\n\n/**\n * Supports waiting on a backoff by:\n *\n * <ul>\n *   <li>Promisifying setTimeout, so we can set a timeout in our Promise chain</li>\n *   <li>Listening on a signal bus for abort events, just like the Fetch API</li>\n *   <li>Failing in the same way the Fetch API fails, so timing out a live request and a throttled\n *       request appear the same.</li>\n * </ul>\n *\n * <p>Visible for testing.\n */\nexport function setAbortableTimeout(\n  signal: RemoteConfigAbortSignal,\n  throttleEndTimeMillis: number\n): Promise<void> {\n  return new Promise((resolve, reject) => {\n    // Derives backoff from given end time, normalizing negative numbers to zero.\n    const backoffMillis = Math.max(throttleEndTimeMillis - Date.now(), 0);\n\n    const timeout = setTimeout(resolve, backoffMillis);\n\n    // Adds listener, rather than sets onabort, because signal is a shared object.\n    signal.addEventListener(() => {\n      clearTimeout(timeout);\n\n      // If the request completes before this timeout, the rejection has no effect.\n      reject(\n        ERROR_FACTORY.create(ErrorCode.FETCH_THROTTLE, {\n          throttleEndTimeMillis\n        })\n      );\n    });\n  });\n}\n\ntype RetriableError = FirebaseError & { customData: { httpStatus: string } };\n/**\n * Returns true if the {@link Error} indicates a fetch request may succeed later.\n */\nfunction isRetriableError(e: Error): e is RetriableError {\n  if (!(e instanceof FirebaseError) || !e.customData) {\n    return false;\n  }\n\n  // Uses string index defined by ErrorData, which FirebaseError implements.\n  const httpStatus = Number(e.customData['httpStatus']);\n\n  return (\n    httpStatus === 429 ||\n    httpStatus === 500 ||\n    httpStatus === 503 ||\n    httpStatus === 504\n  );\n}\n\n/**\n * Decorates a Client with retry logic.\n *\n * <p>Comparable to CachingClient, but uses backoff logic instead of cache max age and doesn't cache\n * responses (because the SDK has no use for error responses).\n */\nexport class RetryingClient implements RemoteConfigFetchClient {\n  constructor(\n    private readonly client: RemoteConfigFetchClient,\n    private readonly storage: Storage\n  ) {}\n\n  async fetch(request: FetchRequest): Promise<FetchResponse> {\n    const throttleMetadata = (await this.storage.getThrottleMetadata()) || {\n      backoffCount: 0,\n      throttleEndTimeMillis: Date.now()\n    };\n\n    return this.attemptFetch(request, throttleMetadata);\n  }\n\n  /**\n   * A recursive helper for attempting a fetch request repeatedly.\n   *\n   * @throws any non-retriable errors.\n   */\n  async attemptFetch(\n    request: FetchRequest,\n    { throttleEndTimeMillis, backoffCount }: ThrottleMetadata\n  ): Promise<FetchResponse> {\n    // Starts with a (potentially zero) timeout to support resumption from stored state.\n    // Ensures the throttle end time is honored if the last attempt timed out.\n    // Note the SDK will never make a request if the fetch timeout expires at this point.\n    await setAbortableTimeout(request.signal, throttleEndTimeMillis);\n\n    try {\n      const response = await this.client.fetch(request);\n\n      // Note the SDK only clears throttle state if response is success or non-retriable.\n      await this.storage.deleteThrottleMetadata();\n\n      return response;\n    } catch (e) {\n      if (!isRetriableError(e as Error)) {\n        throw e;\n      }\n\n      // Increments backoff state.\n      const throttleMetadata = {\n        throttleEndTimeMillis:\n          Date.now() + calculateBackoffMillis(backoffCount),\n        backoffCount: backoffCount + 1\n      };\n\n      // Persists state.\n      await this.storage.setThrottleMetadata(throttleMetadata);\n\n      return this.attemptFetch(request, throttleMetadata);\n    }\n  }\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { FirebaseApp } from '@firebase/app';\nimport {\n  RemoteConfig as RemoteConfigType,\n  FetchStatus,\n  RemoteConfigSettings\n} from './public_types';\nimport { StorageCache } from './storage/storage_cache';\nimport { RemoteConfigFetchClient } from './client/remote_config_fetch_client';\nimport { Storage } from './storage/storage';\nimport { Logger } from '@firebase/logger';\n\nconst DEFAULT_FETCH_TIMEOUT_MILLIS = 60 * 1000; // One minute\nconst DEFAULT_CACHE_MAX_AGE_MILLIS = 12 * 60 * 60 * 1000; // Twelve hours.\n\n/**\n * Encapsulates business logic mapping network and storage dependencies to the public SDK API.\n *\n * See {@link https://github.com/firebase/firebase-js-sdk/blob/main/packages/firebase/compat/index.d.ts|interface documentation} for method descriptions.\n */\nexport class RemoteConfig implements RemoteConfigType {\n  /**\n   * Tracks completion of initialization promise.\n   * @internal\n   */\n  _isInitializationComplete = false;\n\n  /**\n   * De-duplicates initialization calls.\n   * @internal\n   */\n  _initializePromise?: Promise<void>;\n\n  settings: RemoteConfigSettings = {\n    fetchTimeoutMillis: DEFAULT_FETCH_TIMEOUT_MILLIS,\n    minimumFetchIntervalMillis: DEFAULT_CACHE_MAX_AGE_MILLIS\n  };\n\n  defaultConfig: { [key: string]: string | number | boolean } = {};\n\n  get fetchTimeMillis(): number {\n    return this._storageCache.getLastSuccessfulFetchTimestampMillis() || -1;\n  }\n\n  get lastFetchStatus(): FetchStatus {\n    return this._storageCache.getLastFetchStatus() || 'no-fetch-yet';\n  }\n\n  constructor(\n    // Required by FirebaseServiceFactory interface.\n    readonly app: FirebaseApp,\n    // JS doesn't support private yet\n    // (https://github.com/tc39/proposal-class-fields#private-fields), so we hint using an\n    // underscore prefix.\n    /**\n     * @internal\n     */\n    readonly _client: RemoteConfigFetchClient,\n    /**\n     * @internal\n     */\n    readonly _storageCache: StorageCache,\n    /**\n     * @internal\n     */\n    readonly _storage: Storage,\n    /**\n     * @internal\n     */\n    readonly _logger: Logger\n  ) {}\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { FetchStatus, CustomSignals } from '@firebase/remote-config-types';\nimport { FetchResponse, FirebaseRemoteConfigObject } from '../public_types';\nimport { ERROR_FACTORY, ErrorCode } from '../errors';\nimport { RC_CUSTOM_SIGNAL_MAX_ALLOWED_SIGNALS } from '../constants';\nimport { FirebaseError } from '@firebase/util';\n\n/**\n * Converts an error event associated with a {@link IDBRequest} to a {@link FirebaseError}.\n */\nfunction toFirebaseError(event: Event, errorCode: ErrorCode): FirebaseError {\n  const originalError = (event.target as IDBRequest).error || undefined;\n  return ERROR_FACTORY.create(errorCode, {\n    originalErrorMessage: originalError && (originalError as Error)?.message\n  });\n}\n\n/**\n * A general-purpose store keyed by app + namespace + {@link\n * ProjectNamespaceKeyFieldValue}.\n *\n * <p>The Remote Config SDK can be used with multiple app installations, and each app can interact\n * with multiple namespaces, so this store uses app (ID + name) and namespace as common parent keys\n * for a set of key-value pairs. See {@link Storage#createCompositeKey}.\n *\n * <p>Visible for testing.\n */\nexport const APP_NAMESPACE_STORE = 'app_namespace_store';\n\nconst DB_NAME = 'firebase_remote_config';\nconst DB_VERSION = 1;\n\n/**\n * Encapsulates metadata concerning throttled fetch requests.\n */\nexport interface ThrottleMetadata {\n  // The number of times fetch has backed off. Used for resuming backoff after a timeout.\n  backoffCount: number;\n  // The Unix timestamp in milliseconds when callers can retry a request.\n  throttleEndTimeMillis: number;\n}\n\n/**\n * Provides type-safety for the \"key\" field used by {@link APP_NAMESPACE_STORE}.\n *\n * <p>This seems like a small price to avoid potentially subtle bugs caused by a typo.\n */\ntype ProjectNamespaceKeyFieldValue =\n  | 'active_config'\n  | 'active_config_etag'\n  | 'last_fetch_status'\n  | 'last_successful_fetch_timestamp_millis'\n  | 'last_successful_fetch_response'\n  | 'settings'\n  | 'throttle_metadata'\n  | 'custom_signals';\n\n// Visible for testing.\nexport function openDatabase(): Promise<IDBDatabase> {\n  return new Promise((resolve, reject) => {\n    try {\n      const request = indexedDB.open(DB_NAME, DB_VERSION);\n      request.onerror = event => {\n        reject(toFirebaseError(event, ErrorCode.STORAGE_OPEN));\n      };\n      request.onsuccess = event => {\n        resolve((event.target as IDBOpenDBRequest).result);\n      };\n      request.onupgradeneeded = event => {\n        const db = (event.target as IDBOpenDBRequest).result;\n\n        // We don't use 'break' in this switch statement, the fall-through\n        // behavior is what we want, because if there are multiple versions between\n        // the old version and the current version, we want ALL the migrations\n        // that correspond to those versions to run, not only the last one.\n        // eslint-disable-next-line default-case\n        switch (event.oldVersion) {\n          case 0:\n            db.createObjectStore(APP_NAMESPACE_STORE, {\n              keyPath: 'compositeKey'\n            });\n        }\n      };\n    } catch (error) {\n      reject(\n        ERROR_FACTORY.create(ErrorCode.STORAGE_OPEN, {\n          originalErrorMessage: (error as Error)?.message\n        })\n      );\n    }\n  });\n}\n\n/**\n * Abstracts data persistence.\n */\nexport abstract class Storage {\n  getLastFetchStatus(): Promise<FetchStatus | undefined> {\n    return this.get<FetchStatus>('last_fetch_status');\n  }\n\n  setLastFetchStatus(status: FetchStatus): Promise<void> {\n    return this.set<FetchStatus>('last_fetch_status', status);\n  }\n\n  // This is comparable to a cache entry timestamp. If we need to expire other data, we could\n  // consider adding timestamp to all storage records and an optional max age arg to getters.\n  getLastSuccessfulFetchTimestampMillis(): Promise<number | undefined> {\n    return this.get<number>('last_successful_fetch_timestamp_millis');\n  }\n\n  setLastSuccessfulFetchTimestampMillis(timestamp: number): Promise<void> {\n    return this.set<number>(\n      'last_successful_fetch_timestamp_millis',\n      timestamp\n    );\n  }\n\n  getLastSuccessfulFetchResponse(): Promise<FetchResponse | undefined> {\n    return this.get<FetchResponse>('last_successful_fetch_response');\n  }\n\n  setLastSuccessfulFetchResponse(response: FetchResponse): Promise<void> {\n    return this.set<FetchResponse>('last_successful_fetch_response', response);\n  }\n\n  getActiveConfig(): Promise<FirebaseRemoteConfigObject | undefined> {\n    return this.get<FirebaseRemoteConfigObject>('active_config');\n  }\n\n  setActiveConfig(config: FirebaseRemoteConfigObject): Promise<void> {\n    return this.set<FirebaseRemoteConfigObject>('active_config', config);\n  }\n\n  getActiveConfigEtag(): Promise<string | undefined> {\n    return this.get<string>('active_config_etag');\n  }\n\n  setActiveConfigEtag(etag: string): Promise<void> {\n    return this.set<string>('active_config_etag', etag);\n  }\n\n  getThrottleMetadata(): Promise<ThrottleMetadata | undefined> {\n    return this.get<ThrottleMetadata>('throttle_metadata');\n  }\n\n  setThrottleMetadata(metadata: ThrottleMetadata): Promise<void> {\n    return this.set<ThrottleMetadata>('throttle_metadata', metadata);\n  }\n\n  deleteThrottleMetadata(): Promise<void> {\n    return this.delete('throttle_metadata');\n  }\n\n  getCustomSignals(): Promise<CustomSignals | undefined> {\n    return this.get<CustomSignals>('custom_signals');\n  }\n\n  abstract setCustomSignals(\n    customSignals: CustomSignals\n  ): Promise<CustomSignals>;\n  abstract get<T>(key: ProjectNamespaceKeyFieldValue): Promise<T | undefined>;\n  abstract set<T>(key: ProjectNamespaceKeyFieldValue, value: T): Promise<void>;\n  abstract delete(key: ProjectNamespaceKeyFieldValue): Promise<void>;\n}\n\nexport class IndexedDbStorage extends Storage {\n  /**\n   * @param appId enables storage segmentation by app (ID + name).\n   * @param appName enables storage segmentation by app (ID + name).\n   * @param namespace enables storage segmentation by namespace.\n   */\n  constructor(\n    private readonly appId: string,\n    private readonly appName: string,\n    private readonly namespace: string,\n    private readonly openDbPromise = openDatabase()\n  ) {\n    super();\n  }\n\n  async setCustomSignals(customSignals: CustomSignals): Promise<CustomSignals> {\n    const db = await this.openDbPromise;\n    const transaction = db.transaction([APP_NAMESPACE_STORE], 'readwrite');\n    const storedSignals = await this.getWithTransaction<CustomSignals>(\n      'custom_signals',\n      transaction\n    );\n    const updatedSignals = mergeCustomSignals(\n      customSignals,\n      storedSignals || {}\n    );\n    await this.setWithTransaction<CustomSignals>(\n      'custom_signals',\n      updatedSignals,\n      transaction\n    );\n    return updatedSignals;\n  }\n\n  /**\n   * Gets a value from the database using the provided transaction.\n   *\n   * @param key The key of the value to get.\n   * @param transaction The transaction to use for the operation.\n   * @returns The value associated with the key, or undefined if no such value exists.\n   */\n  async getWithTransaction<T>(\n    key: ProjectNamespaceKeyFieldValue,\n    transaction: IDBTransaction\n  ): Promise<T | undefined> {\n    return new Promise((resolve, reject) => {\n      const objectStore = transaction.objectStore(APP_NAMESPACE_STORE);\n      const compositeKey = this.createCompositeKey(key);\n      try {\n        const request = objectStore.get(compositeKey);\n        request.onerror = event => {\n          reject(toFirebaseError(event, ErrorCode.STORAGE_GET));\n        };\n        request.onsuccess = event => {\n          const result = (event.target as IDBRequest).result;\n          if (result) {\n            resolve(result.value);\n          } else {\n            resolve(undefined);\n          }\n        };\n      } catch (e) {\n        reject(\n          ERROR_FACTORY.create(ErrorCode.STORAGE_GET, {\n            originalErrorMessage: (e as Error)?.message\n          })\n        );\n      }\n    });\n  }\n\n  /**\n   * Sets a value in the database using the provided transaction.\n   *\n   * @param key The key of the value to set.\n   * @param value The value to set.\n   * @param transaction The transaction to use for the operation.\n   * @returns A promise that resolves when the operation is complete.\n   */\n  async setWithTransaction<T>(\n    key: ProjectNamespaceKeyFieldValue,\n    value: T,\n    transaction: IDBTransaction\n  ): Promise<void> {\n    return new Promise((resolve, reject) => {\n      const objectStore = transaction.objectStore(APP_NAMESPACE_STORE);\n      const compositeKey = this.createCompositeKey(key);\n      try {\n        const request = objectStore.put({\n          compositeKey,\n          value\n        });\n        request.onerror = (event: Event) => {\n          reject(toFirebaseError(event, ErrorCode.STORAGE_SET));\n        };\n        request.onsuccess = () => {\n          resolve();\n        };\n      } catch (e) {\n        reject(\n          ERROR_FACTORY.create(ErrorCode.STORAGE_SET, {\n            originalErrorMessage: (e as Error)?.message\n          })\n        );\n      }\n    });\n  }\n\n  async get<T>(key: ProjectNamespaceKeyFieldValue): Promise<T | undefined> {\n    const db = await this.openDbPromise;\n    const transaction = db.transaction([APP_NAMESPACE_STORE], 'readonly');\n    return this.getWithTransaction<T>(key, transaction);\n  }\n\n  async set<T>(key: ProjectNamespaceKeyFieldValue, value: T): Promise<void> {\n    const db = await this.openDbPromise;\n    const transaction = db.transaction([APP_NAMESPACE_STORE], 'readwrite');\n    return this.setWithTransaction<T>(key, value, transaction);\n  }\n\n  async delete(key: ProjectNamespaceKeyFieldValue): Promise<void> {\n    const db = await this.openDbPromise;\n    return new Promise((resolve, reject) => {\n      const transaction = db.transaction([APP_NAMESPACE_STORE], 'readwrite');\n      const objectStore = transaction.objectStore(APP_NAMESPACE_STORE);\n      const compositeKey = this.createCompositeKey(key);\n      try {\n        const request = objectStore.delete(compositeKey);\n        request.onerror = (event: Event) => {\n          reject(toFirebaseError(event, ErrorCode.STORAGE_DELETE));\n        };\n        request.onsuccess = () => {\n          resolve();\n        };\n      } catch (e) {\n        reject(\n          ERROR_FACTORY.create(ErrorCode.STORAGE_DELETE, {\n            originalErrorMessage: (e as Error)?.message\n          })\n        );\n      }\n    });\n  }\n\n  // Facilitates composite key functionality (which is unsupported in IE).\n  createCompositeKey(key: ProjectNamespaceKeyFieldValue): string {\n    return [this.appId, this.appName, this.namespace, key].join();\n  }\n}\n\nexport class InMemoryStorage extends Storage {\n  private storage: { [key: string]: unknown } = {};\n\n  async get<T>(key: ProjectNamespaceKeyFieldValue): Promise<T> {\n    return Promise.resolve(this.storage[key] as T);\n  }\n\n  async set<T>(key: ProjectNamespaceKeyFieldValue, value: T): Promise<void> {\n    this.storage[key] = value;\n    return Promise.resolve(undefined);\n  }\n\n  async delete(key: ProjectNamespaceKeyFieldValue): Promise<void> {\n    this.storage[key] = undefined;\n    return Promise.resolve();\n  }\n\n  async setCustomSignals(customSignals: CustomSignals): Promise<CustomSignals> {\n    const storedSignals = (this.storage['custom_signals'] ||\n      {}) as CustomSignals;\n    this.storage['custom_signals'] = mergeCustomSignals(\n      customSignals,\n      storedSignals\n    );\n    return Promise.resolve(this.storage['custom_signals'] as CustomSignals);\n  }\n}\n\nfunction mergeCustomSignals(\n  customSignals: CustomSignals,\n  storedSignals: CustomSignals\n): CustomSignals {\n  const combinedSignals = {\n    ...storedSignals,\n    ...customSignals\n  };\n\n  // Filter out key-value assignments with null values since they are signals being unset\n  const updatedSignals = Object.fromEntries(\n    Object.entries(combinedSignals)\n      .filter(([_, v]) => v !== null)\n      .map(([k, v]) => {\n        // Stringify numbers to store a map of string keys and values which can be sent\n        // as-is in a fetch call.\n        if (typeof v === 'number') {\n          return [k, v.toString()];\n        }\n        return [k, v];\n      })\n  );\n\n  // Throw an error if the number of custom signals to be stored exceeds the limit\n  if (\n    Object.keys(updatedSignals).length > RC_CUSTOM_SIGNAL_MAX_ALLOWED_SIGNALS\n  ) {\n    throw ERROR_FACTORY.create(ErrorCode.CUSTOM_SIGNAL_MAX_ALLOWED_SIGNALS, {\n      maxSignals: RC_CUSTOM_SIGNAL_MAX_ALLOWED_SIGNALS\n    });\n  }\n  return updatedSignals;\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { FetchStatus, CustomSignals } from '@firebase/remote-config-types';\nimport { FirebaseRemoteConfigObject } from '../public_types';\nimport { Storage } from './storage';\n\n/**\n * A memory cache layer over storage to support the SDK's synchronous read requirements.\n */\nexport class StorageCache {\n  constructor(private readonly storage: Storage) {}\n\n  /**\n   * Memory caches.\n   */\n  private lastFetchStatus?: FetchStatus;\n  private lastSuccessfulFetchTimestampMillis?: number;\n  private activeConfig?: FirebaseRemoteConfigObject;\n  private customSignals?: CustomSignals;\n\n  /**\n   * Memory-only getters\n   */\n  getLastFetchStatus(): FetchStatus | undefined {\n    return this.lastFetchStatus;\n  }\n\n  getLastSuccessfulFetchTimestampMillis(): number | undefined {\n    return this.lastSuccessfulFetchTimestampMillis;\n  }\n\n  getActiveConfig(): FirebaseRemoteConfigObject | undefined {\n    return this.activeConfig;\n  }\n\n  getCustomSignals(): CustomSignals | undefined {\n    return this.customSignals;\n  }\n\n  /**\n   * Read-ahead getter\n   */\n  async loadFromStorage(): Promise<void> {\n    const lastFetchStatusPromise = this.storage.getLastFetchStatus();\n    const lastSuccessfulFetchTimestampMillisPromise =\n      this.storage.getLastSuccessfulFetchTimestampMillis();\n    const activeConfigPromise = this.storage.getActiveConfig();\n    const customSignalsPromise = this.storage.getCustomSignals();\n\n    // Note:\n    // 1. we consistently check for undefined to avoid clobbering defined values\n    //   in memory\n    // 2. we defer awaiting to improve readability, as opposed to destructuring\n    //   a Promise.all result, for example\n\n    const lastFetchStatus = await lastFetchStatusPromise;\n    if (lastFetchStatus) {\n      this.lastFetchStatus = lastFetchStatus;\n    }\n\n    const lastSuccessfulFetchTimestampMillis =\n      await lastSuccessfulFetchTimestampMillisPromise;\n    if (lastSuccessfulFetchTimestampMillis) {\n      this.lastSuccessfulFetchTimestampMillis =\n        lastSuccessfulFetchTimestampMillis;\n    }\n\n    const activeConfig = await activeConfigPromise;\n    if (activeConfig) {\n      this.activeConfig = activeConfig;\n    }\n\n    const customSignals = await customSignalsPromise;\n    if (customSignals) {\n      this.customSignals = customSignals;\n    }\n  }\n\n  /**\n   * Write-through setters\n   */\n  setLastFetchStatus(status: FetchStatus): Promise<void> {\n    this.lastFetchStatus = status;\n    return this.storage.setLastFetchStatus(status);\n  }\n\n  setLastSuccessfulFetchTimestampMillis(\n    timestampMillis: number\n  ): Promise<void> {\n    this.lastSuccessfulFetchTimestampMillis = timestampMillis;\n    return this.storage.setLastSuccessfulFetchTimestampMillis(timestampMillis);\n  }\n\n  setActiveConfig(activeConfig: FirebaseRemoteConfigObject): Promise<void> {\n    this.activeConfig = activeConfig;\n    return this.storage.setActiveConfig(activeConfig);\n  }\n\n  async setCustomSignals(customSignals: CustomSignals): Promise<void> {\n    this.customSignals = await this.storage.setCustomSignals(customSignals);\n  }\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { RemoteConfig } from './public_types';\nimport { activate, fetchConfig } from './api';\nimport {\n  getModularInstance,\n  isIndexedDBAvailable,\n  validateIndexedDBOpenable\n} from '@firebase/util';\n\n// This API is put in a separate file, so we can stub fetchConfig and activate in tests.\n// It's not possible to stub standalone functions from the same module.\n/**\n *\n * Performs fetch and activate operations, as a convenience.\n *\n * @param remoteConfig - The {@link RemoteConfig} instance.\n *\n * @returns A `Promise` which resolves to true if the current call activated the fetched configs.\n * If the fetched configs were already activated, the `Promise` will resolve to false.\n *\n * @public\n */\nexport async function fetchAndActivate(\n  remoteConfig: RemoteConfig\n): Promise<boolean> {\n  remoteConfig = getModularInstance(remoteConfig);\n  await fetchConfig(remoteConfig);\n  return activate(remoteConfig);\n}\n\n/**\n * This method provides two different checks:\n *\n * 1. Check if IndexedDB exists in the browser environment.\n * 2. Check if the current browser context allows IndexedDB `open()` calls.\n *\n * @returns A `Promise` which resolves to true if a {@link RemoteConfig} instance\n * can be initialized in this environment, or false if it cannot.\n * @public\n */\nexport async function isSupported(): Promise<boolean> {\n  if (!isIndexedDBAvailable()) {\n    return false;\n  }\n\n  try {\n    const isDBOpenable: boolean = await validateIndexedDBOpenable();\n    return isDBOpenable;\n  } catch (error) {\n    return false;\n  }\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport {\n  _registerComponent,\n  registerVersion,\n  SDK_VERSION\n} from '@firebase/app';\nimport { isIndexedDBAvailable } from '@firebase/util';\nimport {\n  Component,\n  ComponentType,\n  ComponentContainer\n} from '@firebase/component';\nimport { Logger, LogLevel as FirebaseLogLevel } from '@firebase/logger';\nimport { RemoteConfig, RemoteConfigOptions } from './public_types';\nimport { name as packageName, version } from '../package.json';\nimport { ensureInitialized } from './api';\nimport { CachingClient } from './client/caching_client';\nimport { RestClient } from './client/rest_client';\nimport { RetryingClient } from './client/retrying_client';\nimport { RC_COMPONENT_NAME } from './constants';\nimport { ErrorCode, ERROR_FACTORY } from './errors';\nimport { RemoteConfig as RemoteConfigImpl } from './remote_config';\nimport { IndexedDbStorage, InMemoryStorage } from './storage/storage';\nimport { StorageCache } from './storage/storage_cache';\n// This needs to be in the same file that calls `getProvider()` on the component\n// or it will get tree-shaken out.\nimport '@firebase/installations';\n\nexport function registerRemoteConfig(): void {\n  _registerComponent(\n    new Component(\n      RC_COMPONENT_NAME,\n      remoteConfigFactory,\n      ComponentType.PUBLIC\n    ).setMultipleInstances(true)\n  );\n\n  registerVersion(packageName, version);\n  // BUILD_TARGET will be replaced by values like esm, cjs, etc during the compilation\n  registerVersion(packageName, version, '__BUILD_TARGET__');\n\n  function remoteConfigFactory(\n    container: ComponentContainer,\n    { options }: { options?: RemoteConfigOptions }\n  ): RemoteConfig {\n    /* Dependencies */\n    // getImmediate for FirebaseApp will always succeed\n    const app = container.getProvider('app').getImmediate();\n    // The following call will always succeed because rc has `import '@firebase/installations'`\n    const installations = container\n      .getProvider('installations-internal')\n      .getImmediate();\n\n    // Normalizes optional inputs.\n    const { projectId, apiKey, appId } = app.options;\n    if (!projectId) {\n      throw ERROR_FACTORY.create(ErrorCode.REGISTRATION_PROJECT_ID);\n    }\n    if (!apiKey) {\n      throw ERROR_FACTORY.create(ErrorCode.REGISTRATION_API_KEY);\n    }\n    if (!appId) {\n      throw ERROR_FACTORY.create(ErrorCode.REGISTRATION_APP_ID);\n    }\n    const namespace = options?.templateId || 'firebase';\n\n    const storage = isIndexedDBAvailable()\n      ? new IndexedDbStorage(appId, app.name, namespace)\n      : new InMemoryStorage();\n    const storageCache = new StorageCache(storage);\n\n    const logger = new Logger(packageName);\n\n    // Sets ERROR as the default log level.\n    // See RemoteConfig#setLogLevel for corresponding normalization to ERROR log level.\n    logger.logLevel = FirebaseLogLevel.ERROR;\n\n    const restClient = new RestClient(\n      installations,\n      // Uses the JS SDK version, by which the RC package version can be deduced, if necessary.\n      SDK_VERSION,\n      namespace,\n      projectId,\n      apiKey,\n      appId\n    );\n    const retryingClient = new RetryingClient(restClient, storage);\n    const cachingClient = new CachingClient(\n      retryingClient,\n      storage,\n      storageCache,\n      logger\n    );\n\n    const remoteConfigInstance = new RemoteConfigImpl(\n      app,\n      cachingClient,\n      storageCache,\n      storage,\n      logger\n    );\n\n    // Starts warming cache.\n    // eslint-disable-next-line @typescript-eslint/no-floating-promises\n    ensureInitialized(remoteConfigInstance);\n\n    return remoteConfigInstance;\n  }\n}\n", "/**\n * The Firebase Remote Config Web SDK.\n * This SDK does not work in a Node.js environment.\n *\n * @packageDocumentation\n */\n\n/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { registerRemoteConfig } from './register';\n\n// Facilitates debugging by enabling settings changes without rebuilding asset.\n// Note these debug options are not part of a documented, supported API and can change at any time.\n// Consolidates debug options for easier discovery.\n// Uses transient variables on window to avoid lingering state causing panic.\ndeclare global {\n  interface Window {\n    FIREBASE_REMOTE_CONFIG_URL_BASE: string;\n  }\n}\n\nexport * from './api';\nexport * from './api2';\nexport * from './public_types';\n\n/** register component and version */\nregisterRemoteConfig();\n"], "names": ["isIndexedDBAvailable", "indexedDB", "e", "FirebaseError", "Error", "constructor", "code", "message", "customData", "super", "this", "name", "Object", "setPrototypeOf", "prototype", "captureStackTrace", "ErrorFactory", "create", "service", "serviceName", "errors", "data", "fullCode", "template", "replaceTemplate", "replace", "PATTERN", "_", "key", "value", "String", "fullMessage", "deepEqual", "a", "b", "a<PERSON><PERSON><PERSON>", "keys", "b<PERSON><PERSON><PERSON>", "k", "includes", "aProp", "bProp", "isObject", "thing", "calculateBackoffMillis", "backoffCount", "<PERSON><PERSON><PERSON><PERSON>", "backoffFactor", "currBaseValue", "Math", "pow", "randomWait", "round", "random", "min", "getModularInstance", "_delegate", "Component", "instanceFactory", "type", "multipleInstances", "serviceProps", "instantiationMode", "onInstanceCreated", "setInstantiationMode", "mode", "setMultipleInstances", "setServiceProps", "props", "setInstanceCreatedCallback", "callback", "LogLevel", "levelStringToEnum", "debug", "DEBUG", "verbose", "VERBOSE", "info", "INFO", "warn", "WARN", "error", "ERROR", "silent", "SILENT", "defaultLogLevel", "ConsoleMethod", "defaultLogHandler", "instance", "logType", "args", "logLevel", "now", "Date", "toISOString", "method", "console", "<PERSON><PERSON>", "_logLevel", "_log<PERSON><PERSON><PERSON>", "_userLogHandler", "val", "TypeError", "setLogLevel", "log<PERSON><PERSON><PERSON>", "userLogHandler", "log", "idbProxyableTypes", "cursorAdvanceMethods", "cursorRequestMap", "WeakMap", "transactionDoneMap", "transactionStoreNamesMap", "transformCache", "reverseTransformCache", "idbProxyTraps", "get", "target", "prop", "receiver", "IDBTransaction", "objectStoreNames", "undefined", "objectStore", "wrap", "set", "has", "wrapFunction", "func", "IDBDatabase", "transaction", "getCursorAdvanceMethods", "IDBCursor", "advance", "continue", "continuePrimaryKey", "apply", "unwrap", "storeNames", "tx", "call", "sort", "transformCachableValue", "cacheDonePromiseForTransaction", "done", "Promise", "resolve", "reject", "unlisten", "removeEventListener", "complete", "DOMException", "addEventListener", "object", "getIdbProxyableTypes", "IDBObjectStore", "IDBIndex", "some", "c", "Proxy", "IDBRequest", "promisifyRequest", "request", "promise", "success", "result", "then", "catch", "newValue", "readMethods", "writeMethods", "cachedMethods", "Map", "getMethod", "targetFuncName", "useIndex", "isWrite", "async", "storeName", "store", "index", "shift", "all", "replaceTraps", "oldTraps", "PENDING_TIMEOUT_MS", "PACKAGE_VERSION", "version", "INTERNAL_AUTH_VERSION", "TOKEN_EXPIRATION_BUFFER", "ERROR_FACTORY", "isServerError", "getInstallationsEndpoint", "projectId", "extractAuthTokenInfoFromResponse", "response", "token", "requestStatus", "expiresIn", "responseExpiresIn", "Number", "creationTime", "getErrorFromResponse", "requestName", "errorData", "json", "serverCode", "serverMessage", "serverStatus", "status", "getHeaders", "<PERSON><PERSON><PERSON><PERSON>", "Headers", "Accept", "getHeadersWithAuth", "appConfig", "refreshToken", "headers", "append", "getAuthorizationHeader", "retryIfServerError", "fn", "sleep", "ms", "setTimeout", "VALID_FID_PATTERN", "generateFid", "fidByteArray", "Uint8Array", "self", "crypto", "msCrypto", "getRandomValues", "fid", "encode", "b64String", "bufferToBase64UrlSafe", "array", "btoa", "fromCharCode", "substr", "test", "<PERSON><PERSON><PERSON>", "appName", "appId", "fidChangeCallbacks", "fidChanged", "callFidChangeCallbacks", "broadcastFidChange", "channel", "getBroadcastChannel", "broadcastChannel", "BroadcastChannel", "onmessage", "postMessage", "closeBroadcastChannel", "size", "close", "callbacks", "OBJECT_STORE_NAME", "db<PERSON><PERSON><PERSON>", "getDbPromise", "openDB", "blocked", "upgrade", "blocking", "terminated", "open", "openPromise", "event", "oldVersion", "newVersion", "db", "createObjectStore", "oldValue", "put", "remove", "delete", "update", "updateFn", "getInstallationEntry", "installations", "registrationPromise", "installationEntry", "oldEntry", "updateOrCreateInstallationEntry", "entry", "registrationStatus", "clearTimedOutRequest", "entryWithPromise", "triggerRegistrationIfNecessary", "navigator", "onLine", "inProgressEntry", "registrationTime", "registerInstallation", "registeredInstallationEntry", "createInstallationRequest", "heartbeatServiceProvider", "endpoint", "heartbeatService", "getImmediate", "optional", "heartbeatsHeader", "getHeartbeatsHeader", "body", "authVersion", "sdkVersion", "JSON", "stringify", "fetch", "ok", "responseValue", "authToken", "waitUntilFidRegistration", "updateInstallationRequest", "hasInstallationRequestTimedOut", "generateAuthTokenRequest", "getGenerateAuthTokenEndpoint", "installation", "refreshAuthToken", "forceRefresh", "tokenPromise", "isEntryRegistered", "oldAuthToken", "isAuthTokenValid", "isAuthTokenExpired", "waitUntilAuthTokenRequest", "updateAuthTokenRequest", "makeAuthTokenRequestInProgressEntry", "inProgressAuthToken", "requestTime", "fetchAuthTokenFromServer", "updatedInstallationEntry", "hasAuthTokenRequestTimedOut", "getToken", "installationsImpl", "completeInstallationRegistration", "getMissingValueError", "valueName", "INSTALLATIONS_NAME", "publicFactory", "container", "app", "get<PERSON><PERSON><PERSON>", "extractAppConfig", "options", "config<PERSON><PERSON><PERSON>", "keyName", "_get<PERSON><PERSON><PERSON>", "_delete", "internalFactory", "getId", "registerInstallations", "_registerComponent", "registerVersion", "RemoteConfigAbortSignal", "listeners", "listener", "push", "abort", "for<PERSON>ach", "RC_COMPONENT_NAME", "BOOLEAN_TRUTHY_VALUES", "Value", "_source", "_value", "asString", "asBoolean", "indexOf", "toLowerCase", "asNumber", "num", "isNaN", "getSource", "getRemoteConfig", "getApp", "rc<PERSON><PERSON><PERSON>", "isInitialized", "getOptions", "initialize", "rc", "initialFetchResponse", "_initializePromise", "_storage", "setLastSuccessfulFetchResponse", "setActiveConfigEtag", "eTag", "_storageCache", "setLastSuccessfulFetchTimestampMillis", "setLastFetchStatus", "setActiveConfig", "config", "_isInitializationComplete", "activate", "remoteConfig", "lastSuccessfulFetchResponse", "activeConfigEtag", "getLastSuccessfulFetchResponse", "getActiveConfigEtag", "ensureInitialized", "loadFromStorage", "fetchConfig", "abortSignal", "settings", "fetchTimeoutMillis", "customSignals", "getCustomSignals", "_logger", "_client", "cacheMaxAgeMillis", "minimumFetchIntervalMillis", "signal", "lastFetchStatus", "hasErrorCode", "errorCode", "getAll", "getAllKeys", "obj1", "obj2", "getActiveConfig", "defaultConfig", "reduce", "allConfigs", "getValue", "getBoolean", "getNumber", "getString", "activeConfig", "ValueImpl", "FirebaseLogLevel", "setCustomSignals", "length", "CachingClient", "client", "storage", "storageCache", "logger", "isCachedDataFresh", "lastSuccessfulFetchTimestampMillis", "cacheAgeMillis", "getLastSuccessfulFetchTimestampMillis", "storageOperations", "getUserLanguage", "navigator<PERSON><PERSON><PERSON>ge", "languages", "language", "RestClient", "firebaseInstallations", "namespace", "installationId", "installationToken", "url", "window", "FIREBASE_REMOTE_CONFIG_URL_BASE", "requestBody", "sdk_version", "app_instance_id", "app_instance_id_token", "app_id", "language_code", "custom_signals", "fetchPromise", "timeoutPromise", "_resolve", "race", "originalError", "originalErrorMessage", "responseEtag", "state", "responseBody", "httpStatus", "RetryingClient", "throttleMetadata", "getThrottleMetadata", "throttleEnd<PERSON>imeMill<PERSON>", "attemptFetch", "setAbortableTimeout", "backoff<PERSON><PERSON><PERSON>", "max", "timeout", "clearTimeout", "deleteThrottleMetadata", "isRetriableError", "setThrottleMetadata", "RemoteConfig", "fetchTimeMillis", "getLastFetchStatus", "toFirebaseError", "APP_NAMESPACE_STORE", "Storage", "timestamp", "etag", "metadata", "IndexedDbStorage", "openDbPromise", "openDatabase", "onerror", "onsuccess", "onupgradeneeded", "keyP<PERSON>", "updatedSignals", "mergeCustomSignals", "getWithTransaction", "setWithTransaction", "compositeKey", "createCompositeKey", "join", "InMemoryStorage", "storedSignals", "combinedSignals", "fromEntries", "entries", "filter", "v", "map", "toString", "maxSignals", "StorageCache", "lastFetchStatusPromise", "lastSuccessfulFetchTimestampMillisPromise", "activeConfigPromise", "customSignalsPromise", "timestampMillis", "fetchAndActivate", "isSupported", "validateIndexedDBOpenable", "preExist", "DB_CHECK_NAME", "deleteDatabase", "registerRemoteConfig", "remoteConfigFactory", "templateId", "packageName", "restClient", "SDK_VERSION", "retryingClient", "cachingClient", "remoteConfigInstance", "RemoteConfigImpl"], "mappings": "2HA8LgBA,uBACd,IACE,MAA4B,iBAAdC,SACf,CAAC,MAAOC,GACP,OAAO,CACR,CACH,CC3HM,MAAOC,sBAAsBC,MAIjC,WAAAC,CAEWC,EACTC,EAEOC,GAEPC,MAAMF,GALGG,KAAIJ,KAAJA,EAGFI,KAAUF,WAAVA,EAPAE,KAAIC,KAdI,gBA6BfC,OAAOC,eAAeH,KAAMP,cAAcW,WAItCV,MAAMW,mBACRX,MAAMW,kBAAkBL,KAAMM,aAAaF,UAAUG,OAExD,EAGU,MAAAD,aAIX,WAAAX,CACmBa,EACAC,EACAC,GAFAV,KAAOQ,QAAPA,EACAR,KAAWS,YAAXA,EACAT,KAAMU,OAANA,CACf,CAEJ,MAAAH,CACEX,KACGe,GAEH,MAAMb,EAAca,EAAK,IAAoB,CAAA,EACvCC,EAAW,GAAGZ,KAAKQ,WAAWZ,IAC9BiB,EAAWb,KAAKU,OAAOd,GAEvBC,EAAUgB,EAUpB,SAASC,gBAAgBD,EAAkBF,GACzC,OAAOE,EAASE,QAAQC,GAAS,CAACC,EAAGC,KACnC,MAAMC,EAAQR,EAAKO,GACnB,OAAgB,MAATC,EAAgBC,OAAOD,GAAS,IAAID,KAAO,GAEtD,CAf+BJ,CAAgBD,EAAUf,GAAc,QAE7DuB,EAAc,GAAGrB,KAAKS,gBAAgBZ,MAAYe,MAIxD,OAFc,IAAInB,cAAcmB,EAAUS,EAAavB,EAGxD,EAUH,MAAMkB,EAAU,gBC7EA,SAAAM,UAAUC,EAAWC,GACnC,GAAID,IAAMC,EACR,OAAO,EAGT,MAAMC,EAAQvB,OAAOwB,KAAKH,GACpBI,EAAQzB,OAAOwB,KAAKF,GAC1B,IAAK,MAAMI,KAAKH,EAAO,CACrB,IAAKE,EAAME,SAASD,GAClB,OAAO,EAGT,MAAME,EAASP,EAA8BK,GACvCG,EAASP,EAA8BI,GAC7C,GAAII,SAASF,IAAUE,SAASD,IAC9B,IAAKT,UAAUQ,EAAOC,GACpB,OAAO,OAEJ,GAAID,IAAUC,EACnB,OAAO,CAEV,CAED,IAAK,MAAMH,KAAKD,EACd,IAAKF,EAAMI,SAASD,GAClB,OAAO,EAGX,OAAO,CACT,CAEA,SAASI,SAASC,GAChB,OAAiB,OAAVA,GAAmC,iBAAVA,CAClC,CCzCM,SAAUC,uBACdC,EACAC,EAhC8B,IAiC9BC,EA3B6B,GAgC7B,MAAMC,EAAgBF,EAAiBG,KAAKC,IAAIH,EAAeF,GAIzDM,EAAaF,KAAKG,MAnBG,GAuBvBJ,GAGCC,KAAKI,SAAW,IACjB,GAIJ,OAAOJ,KAAKK,IAzCkB,MAyCIN,EAAgBG,EACpD,CCtDM,SAAUI,mBACdrC,GAEA,OAAIA,GAAYA,EAA+BsC,UACrCtC,EAA+BsC,UAEhCtC,CAEX,CCDa,MAAAuC,UAiBX,WAAApD,CACWM,EACA+C,EACAC,GAFAjD,KAAIC,KAAJA,EACAD,KAAegD,gBAAfA,EACAhD,KAAIiD,KAAJA,EAnBXjD,KAAiBkD,mBAAG,EAIpBlD,KAAYmD,aAAe,GAE3BnD,KAAAoD,kBAA2C,OAE3CpD,KAAiBqD,kBAAwC,IAYrD,CAEJ,oBAAAC,CAAqBC,GAEnB,OADAvD,KAAKoD,kBAAoBG,EAClBvD,IACR,CAED,oBAAAwD,CAAqBN,GAEnB,OADAlD,KAAKkD,kBAAoBA,EAClBlD,IACR,CAED,eAAAyD,CAAgBC,GAEd,OADA1D,KAAKmD,aAAeO,EACb1D,IACR,CAED,0BAAA2D,CAA2BC,GAEzB,OADA5D,KAAKqD,kBAAoBO,EAClB5D,IACR,MCfS6D,GAAZ,SAAYA,GACVA,EAAAA,EAAA,MAAA,GAAA,QACAA,EAAAA,EAAA,QAAA,GAAA,UACAA,EAAAA,EAAA,KAAA,GAAA,OACAA,EAAAA,EAAA,KAAA,GAAA,OACAA,EAAAA,EAAA,MAAA,GAAA,QACAA,EAAAA,EAAA,OAAA,GAAA,QACD,CAPD,CAAYA,IAAAA,EAOX,CAAA,IAED,MAAMC,EAA2D,CAC/DC,MAASF,EAASG,MAClBC,QAAWJ,EAASK,QACpBC,KAAQN,EAASO,KACjBC,KAAQR,EAASS,KACjBC,MAASV,EAASW,MAClBC,OAAUZ,EAASa,QAMfC,EAA4Bd,EAASO,KAmBrCQ,EAAgB,CACpB,CAACf,EAASG,OAAQ,MAClB,CAACH,EAASK,SAAU,MACpB,CAACL,EAASO,MAAO,OACjB,CAACP,EAASS,MAAO,OACjB,CAACT,EAASW,OAAQ,SAQdK,kBAAgC,CAACC,EAAUC,KAAYC,KAC3D,GAAID,EAAUD,EAASG,SACrB,OAEF,MAAMC,GAAM,IAAIC,MAAOC,cACjBC,EAAST,EAAcG,GAC7B,IAAIM,EAMF,MAAM,IAAI3F,MACR,8DAA8DqF,MANhEO,QAAQD,GACN,IAAIH,OAASJ,EAAS7E,WACnB+E,EAMN,EAGU,MAAAO,OAOX,WAAA5F,CAAmBM,GAAAD,KAAIC,KAAJA,EAUXD,KAASwF,UAAGb,EAsBZ3E,KAAWyF,YAAeZ,kBAc1B7E,KAAe0F,gBAAsB,IAzC5C,CAOD,YAAIT,GACF,OAAOjF,KAAKwF,SACb,CAED,YAAIP,CAASU,GACX,KAAMA,KAAO9B,GACX,MAAM,IAAI+B,UAAU,kBAAkBD,+BAExC3F,KAAKwF,UAAYG,CAClB,CAGD,WAAAE,CAAYF,GACV3F,KAAKwF,UAA2B,iBAARG,EAAmB7B,EAAkB6B,GAAOA,CACrE,CAOD,cAAIG,GACF,OAAO9F,KAAKyF,WACb,CACD,cAAIK,CAAWH,GACb,GAAmB,mBAARA,EACT,MAAM,IAAIC,UAAU,qDAEtB5F,KAAKyF,YAAcE,CACpB,CAMD,kBAAII,GACF,OAAO/F,KAAK0F,eACb,CACD,kBAAIK,CAAeJ,GACjB3F,KAAK0F,gBAAkBC,CACxB,CAMD,KAAA5B,IAASiB,GACPhF,KAAK0F,iBAAmB1F,KAAK0F,gBAAgB1F,KAAM6D,EAASG,SAAUgB,GACtEhF,KAAKyF,YAAYzF,KAAM6D,EAASG,SAAUgB,EAC3C,CACD,GAAAgB,IAAOhB,GACLhF,KAAK0F,iBACH1F,KAAK0F,gBAAgB1F,KAAM6D,EAASK,WAAYc,GAClDhF,KAAKyF,YAAYzF,KAAM6D,EAASK,WAAYc,EAC7C,CACD,IAAAb,IAAQa,GACNhF,KAAK0F,iBAAmB1F,KAAK0F,gBAAgB1F,KAAM6D,EAASO,QAASY,GACrEhF,KAAKyF,YAAYzF,KAAM6D,EAASO,QAASY,EAC1C,CACD,IAAAX,IAAQW,GACNhF,KAAK0F,iBAAmB1F,KAAK0F,gBAAgB1F,KAAM6D,EAASS,QAASU,GACrEhF,KAAKyF,YAAYzF,KAAM6D,EAASS,QAASU,EAC1C,CACD,KAAAT,IAASS,GACPhF,KAAK0F,iBAAmB1F,KAAK0F,gBAAgB1F,KAAM6D,EAASW,SAAUQ,GACtEhF,KAAKyF,YAAYzF,KAAM6D,EAASW,SAAUQ,EAC3C,EChNH,IAAIiB,EACAC,EAqBJ,MAAMC,EAAmB,IAAIC,QACvBC,EAAqB,IAAID,QACzBE,EAA2B,IAAIF,QAC/BG,EAAiB,IAAIH,QACrBI,EAAwB,IAAIJ,QA0DlC,IAAIK,EAAgB,CAChB,GAAAC,CAAIC,EAAQC,EAAMC,GACd,GAAIF,aAAkBG,eAAgB,CAElC,GAAa,SAATF,EACA,OAAOP,EAAmBK,IAAIC,GAElC,GAAa,qBAATC,EACA,OAAOD,EAAOI,kBAAoBT,EAAyBI,IAAIC,GAGnE,GAAa,UAATC,EACA,OAAOC,EAASE,iBAAiB,QAC3BC,EACAH,EAASI,YAAYJ,EAASE,iBAAiB,GAE5D,CAED,OAAOG,KAAKP,EAAOC,GACtB,EACDO,IAAG,CAACR,EAAQC,EAAMzF,KACdwF,EAAOC,GAAQzF,GACR,GAEXiG,IAAG,CAACT,EAAQC,IACJD,aAAkBG,iBACR,SAATF,GAA4B,UAATA,IAGjBA,KAAQD,GAMvB,SAASU,aAAaC,GAIlB,OAAIA,IAASC,YAAYnH,UAAUoH,aAC7B,qBAAsBV,eAAe1G,UA9G/C,SAASqH,0BACL,OAAQvB,IACHA,EAAuB,CACpBwB,UAAUtH,UAAUuH,QACpBD,UAAUtH,UAAUwH,SACpBF,UAAUtH,UAAUyH,oBAEhC,CAmHQJ,GAA0B5F,SAASyF,GAC5B,YAAatC,GAIhB,OADAsC,EAAKQ,MAAMC,OAAO/H,MAAOgF,GAClBkC,KAAKf,EAAiBO,IAAI1G,MACrC,EAEG,YAAagF,GAGhB,OAAOkC,KAAKI,EAAKQ,MAAMC,OAAO/H,MAAOgF,GACzC,EAvBW,SAAUgD,KAAehD,GAC5B,MAAMiD,EAAKX,EAAKY,KAAKH,OAAO/H,MAAOgI,KAAehD,GAElD,OADAsB,EAAyBa,IAAIc,EAAID,EAAWG,KAAOH,EAAWG,OAAS,CAACH,IACjEd,KAAKe,EAChB,CAoBR,CACA,SAASG,uBAAuBjH,GAC5B,MAAqB,mBAAVA,EACAkG,aAAalG,IAGpBA,aAAiB2F,gBAhGzB,SAASuB,+BAA+BJ,GAEpC,GAAI5B,EAAmBe,IAAIa,GACvB,OACJ,MAAMK,EAAO,IAAIC,SAAQ,CAACC,EAASC,KAC/B,MAAMC,SAAW,KACbT,EAAGU,oBAAoB,WAAYC,UACnCX,EAAGU,oBAAoB,QAASpE,OAChC0D,EAAGU,oBAAoB,QAASpE,MAAM,EAEpCqE,SAAW,KACbJ,IACAE,UAAU,EAERnE,MAAQ,KACVkE,EAAOR,EAAG1D,OAAS,IAAIsE,aAAa,aAAc,eAClDH,UAAU,EAEdT,EAAGa,iBAAiB,WAAYF,UAChCX,EAAGa,iBAAiB,QAASvE,OAC7B0D,EAAGa,iBAAiB,QAASvE,MAAM,IAGvC8B,EAAmBc,IAAIc,EAAIK,EAC/B,CAyEQD,CAA+BlH,GA9JhB4H,EA+JD5H,EA1JtB,SAAS6H,uBACL,OAAQ/C,IACHA,EAAoB,CACjBsB,YACA0B,eACAC,SACAxB,UACAZ,gBAEZ,CAiJ6BkC,GA/JgCG,MAAMC,GAAML,aAAkBK,IAgK5E,IAAIC,MAAMlI,EAAOsF,GAErBtF,GAlKW,IAAC4H,CAmKvB,CACA,SAAS7B,KAAK/F,GAGV,GAAIA,aAAiBmI,WACjB,OA3IR,SAASC,iBAAiBC,GACtB,MAAMC,EAAU,IAAIlB,SAAQ,CAACC,EAASC,KAClC,MAAMC,SAAW,KACbc,EAAQb,oBAAoB,UAAWe,SACvCF,EAAQb,oBAAoB,QAASpE,MAAM,EAEzCmF,QAAU,KACZlB,EAAQtB,KAAKsC,EAAQG,SACrBjB,UAAU,EAERnE,MAAQ,KACVkE,EAAOe,EAAQjF,OACfmE,UAAU,EAEdc,EAAQV,iBAAiB,UAAWY,SACpCF,EAAQV,iBAAiB,QAASvE,MAAM,IAe5C,OAbAkF,EACKG,MAAMzI,IAGHA,aAAiBuG,WACjBvB,EAAiBgB,IAAIhG,EAAOqI,EAC/B,IAGAK,OAAM,SAGXrD,EAAsBW,IAAIsC,EAASD,GAC5BC,CACX,CA4GeF,CAAiBpI,GAG5B,GAAIoF,EAAea,IAAIjG,GACnB,OAAOoF,EAAeG,IAAIvF,GAC9B,MAAM2I,EAAW1B,uBAAuBjH,GAOxC,OAJI2I,IAAa3I,IACboF,EAAeY,IAAIhG,EAAO2I,GAC1BtD,EAAsBW,IAAI2C,EAAU3I,IAEjC2I,CACX,CACA,MAAM/B,OAAU5G,GAAUqF,EAAsBE,IAAIvF,GCrIpD,MAAM4I,EAAc,CAAC,MAAO,SAAU,SAAU,aAAc,SACxDC,EAAe,CAAC,MAAO,MAAO,SAAU,SACxCC,EAAgB,IAAIC,IAC1B,SAASC,UAAUxD,EAAQC,GACvB,KAAMD,aAAkBY,cAClBX,KAAQD,GACM,iBAATC,EACP,OAEJ,GAAIqD,EAAcvD,IAAIE,GAClB,OAAOqD,EAAcvD,IAAIE,GAC7B,MAAMwD,EAAiBxD,EAAK7F,QAAQ,aAAc,IAC5CsJ,EAAWzD,IAASwD,EACpBE,EAAUN,EAAanI,SAASuI,GACtC,KAEEA,KAAmBC,EAAWnB,SAAWD,gBAAgB7I,aACrDkK,IAAWP,EAAYlI,SAASuI,GAClC,OAEJ,MAAM/E,OAASkF,eAAgBC,KAAcxF,GAEzC,MAAMiD,EAAKjI,KAAKwH,YAAYgD,EAAWF,EAAU,YAAc,YAC/D,IAAI3D,EAASsB,EAAGwC,MAQhB,OAPIJ,IACA1D,EAASA,EAAO+D,MAAM1F,EAAK2F,iBAMjBpC,QAAQqC,IAAI,CACtBjE,EAAOyD,MAAmBpF,GAC1BsF,GAAWrC,EAAGK,QACd,EACR,EAEA,OADA2B,EAAc9C,IAAIP,EAAMvB,QACjBA,MACX,ED+BA,SAASwF,aAAajH,GAClB6C,EAAgB7C,EAAS6C,EAC7B,CChCAoE,EAAcC,IAAc,IACrBA,EACHpE,IAAK,CAACC,EAAQC,EAAMC,IAAasD,UAAUxD,EAAQC,IAASkE,EAASpE,IAAIC,EAAQC,EAAMC,GACvFO,IAAK,CAACT,EAAQC,MAAWuD,UAAUxD,EAAQC,IAASkE,EAAS1D,IAAIT,EAAQC,oDCxEhEmE,EAAqB,IAErBC,EAAkB,KAAKC,IACvBC,EAAwB,SAKxBC,EAA0B,KCwB1BC,EAAgB,IAAI9K,aDtBV,gBACK,gBCD2C,CACrE,4BACE,kDACF,iBAA4B,2CAC5B,yBAAoC,mCACpC,iBACE,6FACF,cAAyB,kDACzB,8BACE,6EA4BE,SAAU+K,cAAc9G,GAC5B,OACEA,aAAiB9E,eACjB8E,EAAM3E,KAAKiC,SAAQ,iBAEvB,CCxCgB,SAAAyJ,0BAAyBC,UAAEA,IACzC,MAAO,4DAAqCA,iBAC9C,CAEM,SAAUC,iCACdC,GAEA,MAAO,CACLC,MAAOD,EAASC,MAChBC,cAAsC,EACtCC,WA8DuCC,EA9DMJ,EAASG,UAgEjDE,OAAOD,EAAkB9K,QAAQ,IAAK,SA/D3CgL,aAAc5G,KAAKD,OA6DvB,IAA2C2G,CA3D3C,CAEOtB,eAAeyB,qBACpBC,EACAR,GAEA,MACMS,SADoCT,EAASU,QACpB5H,MAC/B,OAAO6G,EAAc7K,OAAiC,iBAAA,CACpD0L,cACAG,WAAYF,EAAUtM,KACtByM,cAAeH,EAAUrM,QACzByM,aAAcJ,EAAUK,QAE5B,CAEgB,SAAAC,YAAWC,OAAEA,IAC3B,OAAO,IAAIC,QAAQ,CACjB,eAAgB,mBAChBC,OAAQ,mBACR,iBAAkBF,GAEtB,CAEgB,SAAAG,mBACdC,GACAC,aAAEA,IAEF,MAAMC,EAAUP,WAAWK,GAE3B,OADAE,EAAQC,OAAO,gBAmCjB,SAASC,uBAAuBH,GAC9B,MAAO,GAAG5B,KAAyB4B,GACrC,CArCkCG,CAAuBH,IAChDC,CACT,CAeOxC,eAAe2C,mBACpBC,GAEA,MAAMxD,QAAewD,IAErB,OAAIxD,EAAO4C,QAAU,KAAO5C,EAAO4C,OAAS,IAEnCY,IAGFxD,CACT,CCnFM,SAAUyD,MAAMC,GACpB,OAAO,IAAI9E,SAAcC,IACvB8E,WAAW9E,EAAS6E,EAAG,GAE3B,CCHO,MAAME,EAAoB,oBAOjB,SAAAC,cACd,IAGE,MAAMC,EAAe,IAAIC,WAAW,KAElCC,KAAKC,QAAWD,KAAyCE,UACpDC,gBAAgBL,GAGvBA,EAAa,GAAK,IAAcA,EAAa,GAAK,GAElD,MAAMM,EAUV,SAASC,OAAOP,GACd,MAAMQ,EChCF,SAAUC,sBAAsBC,GAEpC,OADYC,KAAKhN,OAAOiN,gBAAgBF,IAC7BpN,QAAQ,MAAO,KAAKA,QAAQ,MAAO,IAChD,CD6BoBmN,CAAsBT,GAIxC,OAAOQ,EAAUK,OAAO,EAAG,GAC7B,CAhBgBN,CAAOP,GAEnB,OAAOF,EAAkBgB,KAAKR,GAAOA,EApBd,EAqBxB,CAAC,MAEA,MAvBuB,EAwBxB,CACH,CEzBM,SAAUS,OAAO3B,GACrB,MAAO,GAAGA,EAAU4B,WAAW5B,EAAU6B,OAC3C,CCDA,MAAMC,EAA2D,IAAIzE,IAMrD,SAAA0E,WAAW/B,EAAsBkB,GAC/C,MAAM7M,EAAMsN,OAAO3B,GAEnBgC,uBAAuB3N,EAAK6M,GAsD9B,SAASe,mBAAmB5N,EAAa6M,GACvC,MAAMgB,EASR,SAASC,uBACFC,GAAoB,qBAAsBtB,OAC7CsB,EAAmB,IAAIC,iBAAiB,yBACxCD,EAAiBE,UAAY3P,IAC3BqP,uBAAuBrP,EAAEmB,KAAKO,IAAK1B,EAAEmB,KAAKoN,IAAI,GAGlD,OAAOkB,CACT,CAjBkBD,GACZD,GACFA,EAAQK,YAAY,CAAElO,MAAK6M,SAiB/B,SAASsB,wBACyB,IAA5BV,EAAmBW,MAAcL,IACnCA,EAAiBM,QACjBN,EAAmB,KAEvB,CApBEI,EACF,CA3DEP,CAAmB5N,EAAK6M,EAC1B,CAyCA,SAASc,uBAAuB3N,EAAa6M,GAC3C,MAAMyB,EAAYb,EAAmBjI,IAAIxF,GACzC,GAAKsO,EAIL,IAAK,MAAM5L,KAAY4L,EACrB5L,EAASmK,EAEb,CAUA,IAAIkB,EAA4C,KCrEhD,MAEMQ,EAAoB,+BAS1B,IAAIC,EAA2D,KAC/D,SAASC,eAgBP,OAfKD,IACHA,ET3BJ,SAASE,OAAO3P,EAAMgL,GAAS4E,QAAEA,EAAOC,QAAEA,EAAOC,SAAEA,EAAQC,WAAEA,GAAe,IACxE,MAAMxG,EAAUjK,UAAU0Q,KAAKhQ,EAAMgL,GAC/BiF,EAAchJ,KAAKsC,GAoBzB,OAnBIsG,GACAtG,EAAQV,iBAAiB,iBAAkBqH,IACvCL,EAAQ5I,KAAKsC,EAAQG,QAASwG,EAAMC,WAAYD,EAAME,WAAYnJ,KAAKsC,EAAQhC,aAAc2I,EAAM,IAGvGN,GACArG,EAAQV,iBAAiB,WAAYqH,GAAUN,EAE/CM,EAAMC,WAAYD,EAAME,WAAYF,KAExCD,EACKtG,MAAM0G,IACHN,GACAM,EAAGxH,iBAAiB,SAAS,IAAMkH,MACnCD,GACAO,EAAGxH,iBAAiB,iBAAkBqH,GAAUJ,EAASI,EAAMC,WAAYD,EAAME,WAAYF,IAChG,IAEAtG,OAAM,SACJqG,CACX,CSIgBN,CAdM,kCACG,EAa+B,CAClDE,QAAS,CAACQ,EAAIF,KAMZ,GACO,IADCA,EAEJE,EAAGC,kBAAkBd,EACxB,KAIAC,CACT,CAeOnF,eAAepD,IACpB0F,EACA1L,GAEA,MAAMD,EAAMsN,OAAO3B,GAEb5E,SADW0H,gBACHnI,YAAYiI,EAAmB,aACvCxI,EAAcgB,EAAGhB,YAAYwI,GAC7Be,QAAkBvJ,EAAYP,IAAIxF,GAQxC,aAPM+F,EAAYwJ,IAAItP,EAAOD,SACvB+G,EAAGK,KAEJkI,GAAYA,EAASzC,MAAQ5M,EAAM4M,KACtCa,WAAW/B,EAAW1L,EAAM4M,KAGvB5M,CACT,CAGOoJ,eAAemG,OAAO7D,GAC3B,MAAM3L,EAAMsN,OAAO3B,GAEb5E,SADW0H,gBACHnI,YAAYiI,EAAmB,mBACvCxH,EAAGhB,YAAYwI,GAAmBkB,OAAOzP,SACzC+G,EAAGK,IACX,CAQOiC,eAAeqG,OACpB/D,EACAgE,GAEA,MAAM3P,EAAMsN,OAAO3B,GAEb5E,SADW0H,gBACHnI,YAAYiI,EAAmB,aACvChF,EAAQxC,EAAGhB,YAAYwI,GACvBe,QAAiD/F,EAAM/D,IAC3DxF,GAEI4I,EAAW+G,EAASL,GAa1B,YAXiBxJ,IAAb8C,QACIW,EAAMkG,OAAOzP,SAEbuJ,EAAMgG,IAAI3G,EAAU5I,SAEtB+G,EAAGK,MAELwB,GAAc0G,GAAYA,EAASzC,MAAQjE,EAASiE,KACtDa,WAAW/B,EAAW/C,EAASiE,KAG1BjE,CACT,CClFOS,eAAeuG,qBACpBC,GAEA,IAAIC,EAEJ,MAAMC,QAA0BL,OAAOG,EAAclE,WAAWqE,IAC9D,MAAMD,EAwBV,SAASE,gCACPD,GAEA,MAAME,EAA2BF,GAAY,CAC3CnD,IAAKP,cACL6D,mBAA6C,GAG/C,OAAOC,qBAAqBF,EAC9B,CAjC8BD,CAAgCD,GACpDK,EAyCV,SAASC,+BACPT,EACAE,GAEA,GAAwC,IAApCA,EAAkBI,mBAAkD,CACtE,IAAKI,UAAUC,OAAQ,CAKrB,MAAO,CACLT,oBACAD,oBALmCzI,QAAQE,OAC3C2C,EAAc7K,OAA6B,gBAM9C,CAGD,MAAMoR,EAA+C,CACnD5D,IAAKkD,EAAkBlD,IACvBsD,mBAA6C,EAC7CO,iBAAkBzM,KAAKD,OAEnB8L,EAkBVzG,eAAesH,qBACbd,EACAE,GAEA,IACE,MAAMa,QCxGHvH,eAAewH,2BACpBlF,UAAEA,EAASmF,yBAAEA,IACbjE,IAAEA,IAEF,MAAMkE,EAAW3G,yBAAyBuB,GAEpCE,EAAUP,WAAWK,GAGrBqF,EAAmBF,EAAyBG,aAAa,CAC7DC,UAAU,IAEZ,GAAIF,EAAkB,CACpB,MAAMG,QAAyBH,EAAiBI,sBAC5CD,GACFtF,EAAQC,OAAO,oBAAqBqF,EAEvC,CAED,MAAME,EAAO,CACXxE,MACAyE,YAAatH,EACbwD,MAAO7B,EAAU6B,MACjB+D,WAAYzH,GAGRxB,EAAuB,CAC3BnE,OAAQ,OACR0H,UACAwF,KAAMG,KAAKC,UAAUJ,IAGjB9G,QAAiByB,oBAAmB,IAAM0F,MAAMX,EAAUzI,KAChE,GAAIiC,EAASoH,GAAI,CACf,MAAMC,QAAkDrH,EAASU,OAOjE,MANiE,CAC/D4B,IAAK+E,EAAc/E,KAAOA,EAC1BsD,mBAA2C,EAC3CvE,aAAcgG,EAAchG,aAC5BiG,UAAWvH,iCAAiCsH,EAAcC,WAG7D,CACC,YAAY/G,qBAAqB,sBAAuBP,EAE5D,CD2D8CsG,CACxChB,EACAE,GAEF,OAAO9J,IAAI4J,EAAclE,UAAWiF,EACrC,CAAC,MAAOtS,GAYP,MAXI6L,cAAc7L,IAAkC,MAA5BA,EAAEM,WAAWsM,iBAG7BsE,OAAOK,EAAclE,iBAGrB1F,IAAI4J,EAAclE,UAAW,CACjCkB,IAAKkD,EAAkBlD,IACvBsD,mBAA6C,IAG3C7R,CACP,CACH,CA1CgCqS,CAC1Bd,EACAY,GAEF,MAAO,CAAEV,kBAAmBU,EAAiBX,sBAC9C,CAAM,OAC+B,IAApCC,EAAkBI,mBAEX,CACLJ,oBACAD,oBAAqBgC,yBAAyBjC,IAGzC,CAAEE,oBAEb,CA9E6BO,CACvBT,EACAE,GAGF,OADAD,EAAsBO,EAAiBP,oBAChCO,EAAiBN,iBAAiB,IAG3C,MLvCyB,KKuCrBA,EAAkBlD,IAEb,CAAEkD,wBAAyBD,GAG7B,CACLC,oBACAD,sBAEJ,CA2FAzG,eAAeyI,yBACbjC,GAMA,IAAIK,QAAiC6B,0BACnClC,EAAclE,WAEhB,KAA+B,IAAxBuE,EAAMC,0BAELjE,MAAM,KAEZgE,QAAc6B,0BAA0BlC,EAAclE,WAGxD,GAA4B,IAAxBuE,EAAMC,mBAAkD,CAE1D,MAAMJ,kBAAEA,EAAiBD,oBAAEA,SACnBF,qBAAqBC,GAE7B,OAAIC,GAIKC,CAEV,CAED,OAAOG,CACT,CAUA,SAAS6B,0BACPpG,GAEA,OAAO+D,OAAO/D,GAAWqE,IACvB,IAAKA,EACH,MAAM9F,EAAc7K,OAAM,0BAE5B,OAAO+Q,qBAAqBJ,EAAS,GAEzC,CAEA,SAASI,qBAAqBF,GAC5B,OAUF,SAAS8B,+BACPjC,GAEA,OACoE,IAAlEA,EAAkBI,oBAClBJ,EAAkBW,iBAAmB7G,EAAqB5F,KAAKD,KAEnE,CAjBMgO,CAA+B9B,GAC1B,CACLrD,IAAKqD,EAAMrD,IACXsD,mBAA6C,GAI1CD,CACT,CEzLO7G,eAAe4I,0BACpBtG,UAAEA,EAASmF,yBAAEA,GACbf,GAEA,MAAMgB,EAuCR,SAASmB,6BACPvG,GACAkB,IAAEA,IAEF,MAAO,GAAGzC,yBAAyBuB,MAAckB,uBACnD,CA5CmBqF,CAA6BvG,EAAWoE,GAEnDlE,EAAUH,mBAAmBC,EAAWoE,GAGxCiB,EAAmBF,EAAyBG,aAAa,CAC7DC,UAAU,IAEZ,GAAIF,EAAkB,CACpB,MAAMG,QAAyBH,EAAiBI,sBAC5CD,GACFtF,EAAQC,OAAO,oBAAqBqF,EAEvC,CAED,MAAME,EAAO,CACXc,aAAc,CACZZ,WAAYzH,EACZ0D,MAAO7B,EAAU6B,QAIflF,EAAuB,CAC3BnE,OAAQ,OACR0H,UACAwF,KAAMG,KAAKC,UAAUJ,IAGjB9G,QAAiByB,oBAAmB,IAAM0F,MAAMX,EAAUzI,KAChE,GAAIiC,EAASoH,GAAI,CAIf,OADErH,uCAFqDC,EAASU,OAIjE,CACC,YAAYH,qBAAqB,sBAAuBP,EAE5D,CCnCOlB,eAAe+I,iBACpBvC,EACAwC,GAAe,GAEf,IAAIC,EACJ,MAAMpC,QAAcR,OAAOG,EAAclE,WAAWqE,IAClD,IAAKuC,kBAAkBvC,GACrB,MAAM9F,EAAc7K,OAAM,kBAG5B,MAAMmT,EAAexC,EAAS6B,UAC9B,IAAKQ,GA+HT,SAASI,iBAAiBZ,GACxB,OACqD,IAAnDA,EAAUpH,gBAKd,SAASiI,mBAAmBb,GAC1B,MAAM7N,EAAMC,KAAKD,MACjB,OACEA,EAAM6N,EAAUhH,cAChBgH,EAAUhH,aAAegH,EAAUnH,UAAY1G,EAAMiG,CAEzD,CAVKyI,CAAmBb,EAExB,CApIyBY,CAAiBD,GAEpC,OAAOxC,EACF,GAA8B,IAA1BwC,EAAa/H,cAGtB,OADA6H,EA0BNjJ,eAAesJ,0BACb9C,EACAwC,GAMA,IAAInC,QAAc0C,uBAAuB/C,EAAclE,WACvD,KAAoC,IAA7BuE,EAAM2B,UAAUpH,qBAEfyB,MAAM,KAEZgE,QAAc0C,uBAAuB/C,EAAclE,WAGrD,MAAMkG,EAAY3B,EAAM2B,UACxB,OAA2B,IAAvBA,EAAUpH,cAEL2H,iBAAiBvC,EAAewC,GAEhCR,CAEX,CAjDqBc,CAA0B9C,EAAewC,GACjDrC,EACF,CAEL,IAAKO,UAAUC,OACb,MAAMtG,EAAc7K,OAAM,eAG5B,MAAMoR,EAkIZ,SAASoC,oCACP7C,GAEA,MAAM8C,EAA2C,CAC/CrI,cAAwC,EACxCsI,YAAa9O,KAAKD,OAEpB,MAAO,IACFgM,EACH6B,UAAWiB,EAEf,CA7I8BD,CAAoC7C,GAE5D,OADAsC,EAsENjJ,eAAe2J,yBACbnD,EACAE,GAEA,IACE,MAAM8B,QAAkBI,yBACtBpC,EACAE,GAEIkD,EAAwD,IACzDlD,EACH8B,aAGF,aADM5L,IAAI4J,EAAclE,UAAWsH,GAC5BpB,CACR,CAAC,MAAOvT,GACP,IACE6L,cAAc7L,IACe,MAA5BA,EAAEM,WAAWsM,YAAkD,MAA5B5M,EAAEM,WAAWsM,WAK5C,CACL,MAAM+H,EAAwD,IACzDlD,EACH8B,UAAW,CAAEpH,cAAa,UAEtBxE,IAAI4J,EAAclE,UAAWsH,EACpC,YAPOzD,OAAOK,EAAclE,WAQ7B,MAAMrN,CACP,CACH,CAtGqB0U,CAAyBnD,EAAeY,GAChDA,CACR,KAMH,OAHkB6B,QACRA,EACLpC,EAAM2B,SAEb,CAyCA,SAASe,uBACPjH,GAEA,OAAO+D,OAAO/D,GAAWqE,IACvB,IAAKuC,kBAAkBvC,GACrB,MAAM9F,EAAc7K,OAAM,kBAI5B,OAmFJ,SAAS6T,4BAA4BrB,GACnC,OACuD,IAArDA,EAAUpH,eACVoH,EAAUkB,YAAclJ,EAAqB5F,KAAKD,KAEtD,CAxFQkP,CADiBlD,EAAS6B,WAErB,IACF7B,EACH6B,UAAW,CAAEpH,cAAa,IAIvBuF,CAAQ,GAEnB,CAoCA,SAASuC,kBACPxC,GAEA,YACwBjK,IAAtBiK,GACgE,IAAhEA,EAAkBI,kBAEtB,CCnJO9G,eAAe8J,SACpBtD,EACAwC,GAAe,GAEf,MAAMe,EAAoBvD,QAS5BxG,eAAegK,iCACbxD,GAEA,MAAMC,oBAAEA,SAA8BF,qBAAqBC,GAEvDC,SAEIA,CAEV,CAjBQuD,CAAiCD,GAKvC,aADwBhB,iBAAiBgB,EAAmBf,IAC3C7H,KACnB,CCWA,SAAS8I,qBAAqBC,GAC5B,OAAOrJ,EAAc7K,OAA4C,4BAAA,CAC/DkU,aAEJ,CC3BA,MAAMC,EAAqB,gBAGrBC,cACJC,IAEA,MAAMC,EAAMD,EAAUE,YAAY,OAAO3C,eAEnCtF,EDfF,SAAUkI,iBAAiBF,GAC/B,IAAKA,IAAQA,EAAIG,QACf,MAAMR,qBAAqB,qBAG7B,IAAKK,EAAI5U,KACP,MAAMuU,qBAAqB,YAI7B,MAAMS,EAA2C,CAC/C,YACA,SACA,SAGF,IAAK,MAAMC,KAAWD,EACpB,IAAKJ,EAAIG,QAAQE,GACf,MAAMV,qBAAqBU,GAI/B,MAAO,CACLzG,QAASoG,EAAI5U,KACbsL,UAAWsJ,EAAIG,QAAQzJ,UACvBkB,OAAQoI,EAAIG,QAAQvI,OACpBiC,MAAOmG,EAAIG,QAAQtG,MAEvB,CCboBqG,CAAiBF,GASnC,MANqD,CACnDA,MACAhI,YACAmF,yBAL+BmD,aAAaN,EAAK,aAMjDO,QAAS,IAAM7M,QAAQC,UAED,EAGpB6M,gBACJT,IAEA,MAAMC,EAAMD,EAAUE,YAAY,OAAO3C,eAEnCpB,EAAgBoE,aAAaN,EAAKH,GAAoBvC,eAM5D,MAJ8D,CAC5DmD,MAAO,IC5BJ/K,eAAe+K,MAAMvE,GAC1B,MAAMuD,EAAoBvD,GACpBE,kBAAEA,EAAiBD,oBAAEA,SAA8BF,qBACvDwD,GAWF,OARItD,EACFA,EAAoBnH,MAAMvE,QAAQf,OAIlC+O,iBAAiBgB,GAAmBzK,MAAMvE,QAAQf,OAG7C0M,EAAkBlD,GAC3B,CDaiBuH,CAAMvE,GACnBsD,SAAWd,GAA2Bc,SAAStD,EAAewC,GAEpC,GAGd,SAAAgC,wBACdC,EACE,IAAIzS,UAAU2R,EAAoBC,cAAoC,WAExEa,EACE,IAAIzS,UAtC4B,yBAwC9BsS,gBAED,WAEL,CE3CAE,GACAE,EAAgBxV,EAAMgL,GAEtBwK,EAAgBxV,EAAMgL,EAAS,uDCYlB,MAAAyK,wBAAb,WAAA/V,GACEK,KAAS2V,UAAsB,EAOhC,CANC,gBAAA7M,CAAiB8M,GACf5V,KAAK2V,UAAUE,KAAKD,EACrB,CACD,KAAAE,GACE9V,KAAK2V,UAAUI,SAAQH,GAAYA,KACpC,ECpCI,MAAMI,EAAoB,gBC+EpB5K,EAAgB,IAAI9K,aAC/B,eACA,gBA5DqE,CACrE,sBAAiC,oCACjC,sBACE,kFACF,0BACE,mEACF,uBACE,wDACF,sBACE,+DACF,eACE,8EACF,cACE,mFACF,cACE,iFACF,iBACE,oFACF,uBACE,mHAEF,gBACE,iGAEF,iBACE,mOAGF,qBACE,kFAEF,eACE,0EACF,yBACE,iDACF,oCACE,qECvDJ,MAIM2V,EAAwB,CAAC,IAAK,OAAQ,IAAK,MAAO,IAAK,MAEhD,MAAAC,MACX,WAAAvW,CACmBwW,EACAC,EARY,IAOZpW,KAAOmW,QAAPA,EACAnW,KAAMoW,OAANA,CACf,CAEJ,QAAAC,GACE,OAAOrW,KAAKoW,MACb,CAED,SAAAE,GACE,MAAqB,WAAjBtW,KAAKmW,SAGFF,EAAsBM,QAAQvW,KAAKoW,OAAOI,gBAAkB,CACpE,CAED,QAAAC,GACE,GAAqB,WAAjBzW,KAAKmW,QACP,OAvB2B,EAyB7B,IAAIO,EAAM5K,OAAO9L,KAAKoW,QAItB,OAHIO,MAAMD,KACRA,EA3B2B,GA6BtBA,CACR,CAED,SAAAE,GACE,OAAO5W,KAAKmW,OACb,ECTG,SAAUU,gBACdhC,EAAmBiC,IACnB9B,EAA+B,CAAA,GAE/BH,EAAMhS,mBAAmBgS,GACzB,MAAMkC,EAAa5B,aAAaN,EAAKmB,GACrC,GAAIe,EAAWC,gBAAiB,CAE9B,GAAI1V,UADmByV,EAAWE,aACJjC,GAC5B,OAAO+B,EAAW5E,eAEpB,MAAM/G,EAAc7K,OAAM,sBAC3B,CACDwW,EAAWG,WAAW,CAAElC,YACxB,MAAMmC,EAAKJ,EAAW5E,eAmBtB,OAjBI6C,EAAQoC,uBAGVD,EAAGE,mBAAqB9O,QAAQqC,IAAI,CAClCuM,EAAGG,SAASC,+BAA+BvC,EAAQoC,sBACnDD,EAAGG,SAASE,oBAAoBxC,EAAQoC,sBAAsBK,MAAQ,IACtEN,EAAGO,cAAcC,sCAAsCxS,KAAKD,OAC5DiS,EAAGO,cAAcE,mBAAmB,WACpCT,EAAGO,cAAcG,gBACf7C,EAAQoC,sBAAsBU,QAAU,MAEzClO,OAGHuN,EAAGY,2BAA4B,GAG1BZ,CACT,CAUO5M,eAAeyN,SAASC,GAC7B,MAAMd,EAAKtU,mBAAmBoV,IACvBC,EAA6BC,SAA0B5P,QAAQqC,IAAI,CACxEuM,EAAGG,SAASc,iCACZjB,EAAGG,SAASe,wBAEd,SACGH,GACAA,EAA4BJ,QAC5BI,EAA4BT,MAC7BS,EAA4BT,OAASU,WAMjC5P,QAAQqC,IAAI,CAChBuM,EAAGO,cAAcG,gBAAgBK,EAA4BJ,QAC7DX,EAAGG,SAASE,oBAAoBU,EAA4BT,SAEvD,EACT,CASM,SAAUa,kBAAkBL,GAChC,MAAMd,EAAKtU,mBAAmBoV,GAM9B,OALKd,EAAGE,qBACNF,EAAGE,mBAAqBF,EAAGO,cAAca,kBAAkB3O,MAAK,KAC9DuN,EAAGY,2BAA4B,CAAI,KAGhCZ,EAAGE,kBACZ,CAOO9M,eAAeiO,YAAYP,GAChC,MAAMd,EAAKtU,mBAAmBoV,GAWxBQ,EAAc,IAAI/C,wBAExBpI,YAAW/C,UAETkO,EAAY3C,OAAO,GAClBqB,EAAGuB,SAASC,oBAEf,MAAMC,EAAgBzB,EAAGO,cAAcmB,mBACnCD,GACFzB,EAAG2B,QAAQ/U,MACT,wCAAwC2O,KAAKC,UAAUiG,MAI3D,UACQzB,EAAG4B,QAAQnG,MAAM,CACrBoG,kBAAmB7B,EAAGuB,SAASO,2BAC/BC,OAAQT,EACRG,wBAGIzB,EAAGO,cAAcE,mBAAmB,UAC3C,CAAC,MAAOpY,GACP,MAAM2Z,EFnEM,SAAAC,aAAa5Z,EAAU6Z,GACrC,OAAO7Z,aAAaC,gBAAgD,IAA/BD,EAAEI,KAAK2W,QAAQ8C,EACtD,CEiE4BD,CAAa5Z,EAAqC,kBACtE,WACA,UAEJ,YADM2X,EAAGO,cAAcE,mBAAmBuB,GACpC3Z,CACP,CACH,CAUM,SAAU8Z,OAAOrB,GACrB,MAAMd,EAAKtU,mBAAmBoV,GAC9B,OAkHF,SAASsB,WAAWC,EAAW,GAAIC,EAAW,CAAA,GAC5C,OAAOvZ,OAAOwB,KAAK,IAAK8X,KAASC,GACnC,CApHSF,CACLpC,EAAGO,cAAcgC,kBACjBvC,EAAGwC,eACHC,QAAO,CAACC,EAAY3Y,KACpB2Y,EAAW3Y,GAAO4Y,SAAS7B,EAAc/W,GAClC2Y,IACN,CAA2B,EAChC,CAagB,SAAAE,WAAW9B,EAA4B/W,GACrD,OAAO4Y,SAASjX,mBAAmBoV,GAAe/W,GAAKoV,WACzD,CAcgB,SAAA0D,UAAU/B,EAA4B/W,GACpD,OAAO4Y,SAASjX,mBAAmBoV,GAAe/W,GAAKuV,UACzD,CAagB,SAAAwD,UAAUhC,EAA4B/W,GACpD,OAAO4Y,SAASjX,mBAAmBoV,GAAe/W,GAAKmV,UACzD,CAYgB,SAAAyD,SAAS7B,EAA4B/W,GACnD,MAAMiW,EAAKtU,mBAAmBoV,GACzBd,EAAGY,2BACNZ,EAAG2B,QAAQ/U,MACT,kCAAkC7C,6HAItC,MAAMgZ,EAAe/C,EAAGO,cAAcgC,kBACtC,OAAIQ,QAAsClT,IAAtBkT,EAAahZ,GACxB,IAAIiZ,MAAU,SAAUD,EAAahZ,IACnCiW,EAAGwC,oBAA2C3S,IAA1BmQ,EAAGwC,cAAczY,GACvC,IAAIiZ,MAAU,UAAW/Y,OAAO+V,EAAGwC,cAAczY,MAE1DiW,EAAG2B,QAAQ/U,MACT,mCAAmC7C,kEAG9B,IAAIiZ,MAAU,UACvB,CAUgB,SAAAtU,YACdoS,EACAhT,GAEA,MAAMkS,EAAKtU,mBAAmBoV,GAC9B,OAAQhT,GACN,IAAK,QACHkS,EAAG2B,QAAQ7T,SAAWmV,EAAiBpW,MACvC,MACF,IAAK,SACHmT,EAAG2B,QAAQ7T,SAAWmV,EAAiB1V,OACvC,MACF,QACEyS,EAAG2B,QAAQ7T,SAAWmV,EAAiB5V,MAE7C,CAmBO+F,eAAe8P,iBACpBpC,EACAW,GAEA,MAAMzB,EAAKtU,mBAAmBoV,GAC9B,GAA0C,IAAtC/X,OAAOwB,KAAKkX,GAAe0B,OAA/B,CAKA,IAAK,MAAMpZ,KAAO0X,EAAe,CAC/B,GAAI1X,EAAIoZ,OHpTmC,IGwTzC,YAHAnD,EAAG2B,QAAQvU,MACT,qBAAqBrD,6CAIzB,MAAMC,EAAQyX,EAAc1X,GAC5B,GACmB,iBAAVC,GACPA,EAAMmZ,OH5TqC,IGiU3C,YAHAnD,EAAG2B,QAAQvU,MACT,oCAAoCrD,4CAIzC,CAED,UACQiW,EAAGO,cAAc2C,iBAAiBzB,EACzC,CAAC,MAAOrU,GACP4S,EAAG2B,QAAQvU,MACT,mDAAmDA,IAEtD,CA5BA,CA6BH,CC/Ta,MAAAgW,cACX,WAAA5a,CACmB6a,EACAC,EACAC,EACAC,GAHA3a,KAAMwa,OAANA,EACAxa,KAAOya,QAAPA,EACAza,KAAY0a,aAAZA,EACA1a,KAAM2a,OAANA,CACf,CAWJ,iBAAAC,CACE5B,EACA6B,GAGA,IAAKA,EAEH,OADA7a,KAAK2a,OAAO5W,MAAM,iDACX,EAIT,MAAM+W,EAAiB3V,KAAKD,MAAQ2V,EAE9BD,EAAoBE,GAAkB9B,EAS5C,OAPAhZ,KAAK2a,OAAO5W,MAER,+CAAsB+W,iEACyC9B,oBAC7C4B,MAGfA,CACR,CAED,WAAMhI,CAAMpJ,GAEV,MAAOqR,EAAoC3C,SACnC3P,QAAQqC,IAAI,CAChB5K,KAAKya,QAAQM,wCACb/a,KAAKya,QAAQrC,mCAIjB,GACEF,GACAlY,KAAK4a,kBACHpR,EAAQwP,kBACR6B,GAGF,OAAO3C,EAKT1O,EAAQiO,KACNS,GAA+BA,EAA4BT,KAG7D,MAAMhM,QAAiBzL,KAAKwa,OAAO5H,MAAMpJ,GAInCwR,EAAoB,CAExBhb,KAAK0a,aAAa/C,sCAAsCxS,KAAKD,QAY/D,OATwB,MAApBuG,EAASc,QAEXyO,EAAkBnF,KAChB7V,KAAKya,QAAQlD,+BAA+B9L,UAI1ClD,QAAQqC,IAAIoQ,GAEXvP,CACR,EC7Fa,SAAAwP,gBACdC,EAAuCzJ,WAEvC,OAEGyJ,EAAkBC,WAAaD,EAAkBC,UAAU,IAG5DD,EAAkBE,QAGtB,CCgBa,MAAAC,WACX,WAAA1b,CACmB2b,EACA7I,EACA8I,EACAhQ,EACAkB,EACAiC,GALA1O,KAAqBsb,sBAArBA,EACAtb,KAAUyS,WAAVA,EACAzS,KAASub,UAATA,EACAvb,KAASuL,UAATA,EACAvL,KAAMyM,OAANA,EACAzM,KAAK0O,MAALA,CACf,CAWJ,WAAMkE,CAAMpJ,GACV,MAAOgS,EAAgBC,SAA2BlT,QAAQqC,IAAI,CAC5D5K,KAAKsb,sBAAsBhG,QAC3BtV,KAAKsb,sBAAsBjH,aAOvBqH,EAAM,GAHVC,OAAOC,iCACP,6DAEoC5b,KAAKuL,wBAAwBvL,KAAKub,uBAAuBvb,KAAKyM,SAE9FM,EAAU,CACd,eAAgB,mBAChB,mBAAoB,OAGpB,gBAAiBvD,EAAQiO,MAAQ,KAG7BoE,EAAgC,CAEpCC,YAAa9b,KAAKyS,WAClBsJ,gBAAiBP,EACjBQ,sBAAuBP,EACvBQ,OAAQjc,KAAK0O,MACbwN,cAAejB,kBACfkB,eAAgB3S,EAAQoP,eAIpB5D,EAAU,CACd3P,OAAQ,OACR0H,UACAwF,KAAMG,KAAKC,UAAUkJ,IAIjBO,EAAexJ,MAAM8I,EAAK1G,GAC1BqH,EAAiB,IAAI9T,SAAQ,CAAC+T,EAAU7T,KAE5Ce,EAAQ0P,OAAOpQ,kBAAiB,KAE9B,MAAMvE,EAAQ,IAAI7E,MAAM,8BACxB6E,EAAMtE,KAAO,aACbwI,EAAOlE,EAAM,GACb,IAGJ,IAAIkH,EACJ,UACQlD,QAAQgU,KAAK,CAACH,EAAcC,IAClC5Q,QAAiB2Q,CAClB,CAAC,MAAOI,GACP,IAAInD,EAAoC,uBAIxC,KAHuC,eAAlCmD,GAAyBvc,OAC5BoZ,EAAoC,iBAEhCjO,EAAc7K,OAAO8Y,EAAW,CACpCoD,qBAAuBD,GAAyB3c,SAEnD,CAED,IAAI0M,EAASd,EAASc,OAGtB,MAAMmQ,EAAejR,EAASsB,QAAQrG,IAAI,cAAWM,EAErD,IAAI8Q,EACA6E,EAIJ,GAAwB,MAApBlR,EAASc,OAAgB,CAC3B,IAAIqQ,EACJ,IACEA,QAAqBnR,EAASU,MAC/B,CAAC,MAAOqQ,GACP,MAAMpR,EAAc7K,OAA8B,qBAAA,CAChDkc,qBAAuBD,GAAyB3c,SAEnD,CACDiY,EAAS8E,EAAsB,QAC/BD,EAAQC,EAAoB,KAC7B,CAgBD,GAbc,+BAAVD,EACFpQ,EAAS,IACU,cAAVoQ,EACTpQ,EAAS,IACU,gBAAVoQ,GAAqC,iBAAVA,IAEpC7E,EAAS,CAAA,GAOI,MAAXvL,GAA6B,MAAXA,EACpB,MAAMnB,EAAc7K,OAA+B,eAAA,CACjDsc,WAAYtQ,IAIhB,MAAO,CAAEA,SAAQkL,KAAMiF,EAAc5E,SACtC,EC1FU,MAAAgF,eACX,WAAAnd,CACmB6a,EACAC,GADAza,KAAMwa,OAANA,EACAxa,KAAOya,QAAPA,CACf,CAEJ,WAAM7H,CAAMpJ,GACV,MAAMuT,QAA0B/c,KAAKya,QAAQuC,uBAA0B,CACrE7a,aAAc,EACd8a,sBAAuB9X,KAAKD,OAG9B,OAAOlF,KAAKkd,aAAa1T,EAASuT,EACnC,CAOD,kBAAMG,CACJ1T,GACAyT,sBAAEA,EAAqB9a,aAAEA,UAxEb,SAAAgb,oBACdjE,EACA+D,GAEA,OAAO,IAAI1U,SAAQ,CAACC,EAASC,KAE3B,MAAM2U,EAAgB7a,KAAK8a,IAAIJ,EAAwB9X,KAAKD,MAAO,GAE7DoY,EAAUhQ,WAAW9E,EAAS4U,GAGpClE,EAAOpQ,kBAAiB,KACtByU,aAAaD,GAGb7U,EACE2C,EAAc7K,OAAiC,iBAAA,CAC7C0c,0BAEH,GACD,GAEN,CAuDUE,CAAoB3T,EAAQ0P,OAAQ+D,GAE1C,IACE,MAAMxR,QAAiBzL,KAAKwa,OAAO5H,MAAMpJ,GAKzC,aAFMxJ,KAAKya,QAAQ+C,yBAEZ/R,CACR,CAAC,MAAOjM,GACP,IA3DN,SAASie,iBAAiBje,GACxB,KAAMA,aAAaC,eAAmBD,EAAEM,YACtC,OAAO,EAIT,MAAM+c,EAAa/Q,OAAOtM,EAAEM,WAAuB,YAEnD,OACiB,MAAf+c,GACe,MAAfA,GACe,MAAfA,GACe,MAAfA,CAEJ,CA6CWY,CAAiBje,GACpB,MAAMA,EAIR,MAAMud,EAAmB,CACvBE,sBACE9X,KAAKD,MAAQhD,uBAAuBC,GACtCA,aAAcA,EAAe,GAM/B,aAFMnC,KAAKya,QAAQiD,oBAAoBX,GAEhC/c,KAAKkd,aAAa1T,EAASuT,EACnC,CACF,EC1GU,MAAAY,aAoBX,mBAAIC,GACF,OAAO5d,KAAK0X,cAAcqD,0CAA4C,CACvE,CAED,mBAAI5B,GACF,OAAOnZ,KAAK0X,cAAcmG,sBAAwB,cACnD,CAED,WAAAle,CAEWkV,EAOAkE,EAIArB,EAIAJ,EAIAwB,GAnBA9Y,KAAG6U,IAAHA,EAOA7U,KAAO+Y,QAAPA,EAIA/Y,KAAa0X,cAAbA,EAIA1X,KAAQsX,SAARA,EAIAtX,KAAO8Y,QAAPA,EA5CX9Y,KAAyB+X,2BAAG,EAQ5B/X,KAAA0Y,SAAiC,CAC/BC,mBAtBiC,IAuBjCM,2BAtBiC,OAyBnCjZ,KAAa2Z,cAAiD,EAgC1D,EC5DN,SAASmE,gBAAgB3N,EAAckJ,GACrC,MAAMmD,EAAiBrM,EAAMxJ,OAAsBpC,YAASyC,EAC5D,OAAOoE,EAAc7K,OAAO8Y,EAAW,CACrCoD,qBAAsBD,GAAkBA,GAAyB3c,SAErE,CAYO,MAAMke,EAAsB,sBAqEb,MAAAC,QACpB,kBAAAH,GACE,OAAO7d,KAAK0G,IAAiB,oBAC9B,CAED,kBAAAkR,CAAmBrL,GACjB,OAAOvM,KAAKmH,IAAiB,oBAAqBoF,EACnD,CAID,qCAAAwO,GACE,OAAO/a,KAAK0G,IAAY,yCACzB,CAED,qCAAAiR,CAAsCsG,GACpC,OAAOje,KAAKmH,IACV,yCACA8W,EAEH,CAED,8BAAA7F,GACE,OAAOpY,KAAK0G,IAAmB,iCAChC,CAED,8BAAA6Q,CAA+B9L,GAC7B,OAAOzL,KAAKmH,IAAmB,iCAAkCsE,EAClE,CAED,eAAAiO,GACE,OAAO1Z,KAAK0G,IAAgC,gBAC7C,CAED,eAAAmR,CAAgBC,GACd,OAAO9X,KAAKmH,IAAgC,gBAAiB2Q,EAC9D,CAED,mBAAAO,GACE,OAAOrY,KAAK0G,IAAY,qBACzB,CAED,mBAAA8Q,CAAoB0G,GAClB,OAAOle,KAAKmH,IAAY,qBAAsB+W,EAC/C,CAED,mBAAAlB,GACE,OAAOhd,KAAK0G,IAAsB,oBACnC,CAED,mBAAAgX,CAAoBS,GAClB,OAAOne,KAAKmH,IAAsB,oBAAqBgX,EACxD,CAED,sBAAAX,GACE,OAAOxd,KAAK2Q,OAAO,oBACpB,CAED,gBAAAkI,GACE,OAAO7Y,KAAK0G,IAAmB,iBAChC,EAUG,MAAO0X,yBAAyBJ,QAMpC,WAAAre,CACmB+O,EACAD,EACA8M,EACA8C,EAtHL,SAAAC,eACd,OAAO,IAAI/V,SAAQ,CAACC,EAASC,KAC3B,IACE,MAAMe,EAAUjK,UAAU0Q,KAhChB,yBACG,GAgCbzG,EAAQ+U,QAAUpO,IAChB1H,EAAOqV,gBAAgB3N,EAAK,gBAA0B,EAExD3G,EAAQgV,UAAYrO,IAClB3H,EAAS2H,EAAMxJ,OAA4BgD,OAAO,EAEpDH,EAAQiV,gBAAkBtO,IACxB,MAAMG,EAAMH,EAAMxJ,OAA4BgD,OAQvC,IADCwG,EAAMC,YAEVE,EAAGC,kBAAkBwN,EAAqB,CACxCW,QAAS,gBAEd,CAEJ,CAAC,MAAOna,GACPkE,EACE2C,EAAc7K,OAA+B,eAAA,CAC3Ckc,qBAAuBlY,GAAiB1E,UAG7C,IAEL,CAqFqCye,IAEjCve,QALiBC,KAAK0O,MAALA,EACA1O,KAAOyO,QAAPA,EACAzO,KAASub,UAATA,EACAvb,KAAaqe,cAAbA,CAGlB,CAED,sBAAMhE,CAAiBzB,GACrB,MACMpR,SADWxH,KAAKqe,eACC7W,YAAY,CAACuW,GAAsB,aAKpDY,EAAiBC,mBACrBhG,QAL0B5Y,KAAK6e,mBAC/B,iBACArX,IAIiB,CAAE,GAOrB,aALMxH,KAAK8e,mBACT,iBACAH,EACAnX,GAEKmX,CACR,CASD,wBAAME,CACJ3d,EACAsG,GAEA,OAAO,IAAIe,SAAQ,CAACC,EAASC,KAC3B,MAAMxB,EAAcO,EAAYP,YAAY8W,GACtCgB,EAAe/e,KAAKgf,mBAAmB9d,GAC7C,IACE,MAAMsI,EAAUvC,EAAYP,IAAIqY,GAChCvV,EAAQ+U,QAAUpO,IAChB1H,EAAOqV,gBAAgB3N,EAAK,eAAyB,EAEvD3G,EAAQgV,UAAYrO,IAClB,MAAMxG,EAAUwG,EAAMxJ,OAAsBgD,OAE1CnB,EADEmB,EACMA,EAAOxI,WAEP6F,EACT,CAEJ,CAAC,MAAOxH,GACPiJ,EACE2C,EAAc7K,OAA8B,cAAA,CAC1Ckc,qBAAuBjd,GAAaK,UAGzC,IAEJ,CAUD,wBAAMif,CACJ5d,EACAC,EACAqG,GAEA,OAAO,IAAIe,SAAQ,CAACC,EAASC,KAC3B,MAAMxB,EAAcO,EAAYP,YAAY8W,GACtCgB,EAAe/e,KAAKgf,mBAAmB9d,GAC7C,IACE,MAAMsI,EAAUvC,EAAYwJ,IAAI,CAC9BsO,eACA5d,UAEFqI,EAAQ+U,QAAWpO,IACjB1H,EAAOqV,gBAAgB3N,EAAK,eAAyB,EAEvD3G,EAAQgV,UAAY,KAClBhW,GAAS,CAEZ,CAAC,MAAOhJ,GACPiJ,EACE2C,EAAc7K,OAA8B,cAAA,CAC1Ckc,qBAAuBjd,GAAaK,UAGzC,IAEJ,CAED,SAAM6G,CAAOxF,GACX,MACMsG,SADWxH,KAAKqe,eACC7W,YAAY,CAACuW,GAAsB,YAC1D,OAAO/d,KAAK6e,mBAAsB3d,EAAKsG,EACxC,CAED,SAAML,CAAOjG,EAAoCC,GAC/C,MACMqG,SADWxH,KAAKqe,eACC7W,YAAY,CAACuW,GAAsB,aAC1D,OAAO/d,KAAK8e,mBAAsB5d,EAAKC,EAAOqG,EAC/C,CAED,YAAM,CAAOtG,GACX,MAAMoP,QAAWtQ,KAAKqe,cACtB,OAAO,IAAI9V,SAAQ,CAACC,EAASC,KAC3B,MACMxB,EADcqJ,EAAG9I,YAAY,CAACuW,GAAsB,aAC1B9W,YAAY8W,GACtCgB,EAAe/e,KAAKgf,mBAAmB9d,GAC7C,IACE,MAAMsI,EAAUvC,EAAY0J,OAAOoO,GACnCvV,EAAQ+U,QAAWpO,IACjB1H,EAAOqV,gBAAgB3N,EAAK,kBAA4B,EAE1D3G,EAAQgV,UAAY,KAClBhW,GAAS,CAEZ,CAAC,MAAOhJ,GACPiJ,EACE2C,EAAc7K,OAAiC,iBAAA,CAC7Ckc,qBAAuBjd,GAAaK,UAGzC,IAEJ,CAGD,kBAAAmf,CAAmB9d,GACjB,MAAO,CAAClB,KAAK0O,MAAO1O,KAAKyO,QAASzO,KAAKub,UAAWra,GAAK+d,MACxD,EAGG,MAAOC,wBAAwBlB,QAArC,WAAAre,uBACUK,KAAOya,QAA+B,EAyB/C,CAvBC,SAAM/T,CAAOxF,GACX,OAAOqH,QAAQC,QAAQxI,KAAKya,QAAQvZ,GACrC,CAED,SAAMiG,CAAOjG,EAAoCC,GAE/C,OADAnB,KAAKya,QAAQvZ,GAAOC,EACboH,QAAQC,aAAQxB,EACxB,CAED,YAAM,CAAO9F,GAEX,OADAlB,KAAKya,QAAQvZ,QAAO8F,EACbuB,QAAQC,SAChB,CAED,sBAAM6R,CAAiBzB,GACrB,MAAMuG,EAAiBnf,KAAKya,QAAwB,gBAClD,CAAA,EAKF,OAJAza,KAAKya,QAAwB,eAAImE,mBAC/BhG,EACAuG,GAEK5W,QAAQC,QAAQxI,KAAKya,QAAwB,eACrD,EAGH,SAASmE,mBACPhG,EACAuG,GAEA,MAAMC,EAAkB,IACnBD,KACAvG,GAIC+F,EAAiBze,OAAOmf,YAC5Bnf,OAAOof,QAAQF,GACZG,QAAO,EAAEte,EAAGue,KAAa,OAANA,IACnBC,KAAI,EAAE7d,EAAG4d,KAGS,iBAANA,EACF,CAAC5d,EAAG4d,EAAEE,YAER,CAAC9d,EAAG4d,MAKjB,GACEtf,OAAOwB,KAAKid,GAAgBrE,OT/WoB,ISiXhD,MAAMlP,EAAc7K,OAAoD,oCAAA,CACtEof,WTlX8C,MSqXlD,OAAOhB,CACT,CChXa,MAAAiB,aACX,WAAAjgB,CAA6B8a,GAAAza,KAAOya,QAAPA,CAAoB,CAajD,kBAAAoD,GACE,OAAO7d,KAAKmZ,eACb,CAED,qCAAA4B,GACE,OAAO/a,KAAK6a,kCACb,CAED,eAAAnB,GACE,OAAO1Z,KAAKka,YACb,CAED,gBAAArB,GACE,OAAO7Y,KAAK4Y,aACb,CAKD,qBAAML,GACJ,MAAMsH,EAAyB7f,KAAKya,QAAQoD,qBACtCiC,EACJ9f,KAAKya,QAAQM,wCACTgF,EAAsB/f,KAAKya,QAAQf,kBACnCsG,EAAuBhgB,KAAKya,QAAQ5B,mBAQpCM,QAAwB0G,EAC1B1G,IACFnZ,KAAKmZ,gBAAkBA,GAGzB,MAAM0B,QACEiF,EACJjF,IACF7a,KAAK6a,mCACHA,GAGJ,MAAMX,QAAqB6F,EACvB7F,IACFla,KAAKka,aAAeA,GAGtB,MAAMtB,QAAsBoH,EACxBpH,IACF5Y,KAAK4Y,cAAgBA,EAExB,CAKD,kBAAAhB,CAAmBrL,GAEjB,OADAvM,KAAKmZ,gBAAkB5M,EAChBvM,KAAKya,QAAQ7C,mBAAmBrL,EACxC,CAED,qCAAAoL,CACEsI,GAGA,OADAjgB,KAAK6a,mCAAqCoF,EACnCjgB,KAAKya,QAAQ9C,sCAAsCsI,EAC3D,CAED,eAAApI,CAAgBqC,GAEd,OADAla,KAAKka,aAAeA,EACbla,KAAKya,QAAQ5C,gBAAgBqC,EACrC,CAED,sBAAMG,CAAiBzB,GACrB5Y,KAAK4Y,oBAAsB5Y,KAAKya,QAAQJ,iBAAiBzB,EAC1D,EC7EIrO,eAAe2V,iBACpBjI,GAIA,OAFAA,EAAepV,mBAAmBoV,SAC5BO,YAAYP,GACXD,SAASC,EAClB,CAYO1N,eAAe4V,cACpB,IAAK7gB,uBACH,OAAO,EAGT,IAEE,sBvC8IY8gB,4BACd,OAAO,IAAI7X,SAAQ,CAACC,EAASC,KAC3B,IACE,IAAI4X,GAAoB,EACxB,MAAMC,EACJ,0DACI9W,EAAUmE,KAAKpO,UAAU0Q,KAAKqQ,GACpC9W,EAAQgV,UAAY,KAClBhV,EAAQG,OAAO4F,QAEV8Q,GACH1S,KAAKpO,UAAUghB,eAAeD,GAEhC9X,GAAQ,EAAK,EAEfgB,EAAQiV,gBAAkB,KACxB4B,GAAW,CAAK,EAGlB7W,EAAQ+U,QAAU,KAChB9V,EAAOe,EAAQjF,OAAO1E,SAAW,GAAG,CAEvC,CAAC,MAAO0E,GACPkE,EAAOlE,EACR,IAEL,CuCzKwC6b,EAErC,CAAC,MAAO7b,GACP,OAAO,CACR,CACH,ECxBgB,SAAAic,uBACdhL,EACE,IAAIzS,UACFiT,GAUJ,SAASyK,oBACP7L,GACAI,QAAEA,IAIF,MAAMH,EAAMD,EAAUE,YAAY,OAAO3C,eAEnCpB,EAAgB6D,EACnBE,YAAY,0BACZ3C,gBAGG5G,UAAEA,EAASkB,OAAEA,EAAMiC,MAAEA,GAAUmG,EAAIG,QACzC,IAAKzJ,EACH,MAAMH,EAAc7K,OAAM,2BAE5B,IAAKkM,EACH,MAAMrB,EAAc7K,OAAM,wBAE5B,IAAKmO,EACH,MAAMtD,EAAc7K,OAAM,uBAE5B,MAAMgb,EAAYvG,GAAS0L,YAAc,WAEnCjG,EAAUnb,uBACZ,IAAI8e,iBAAiB1P,EAAOmG,EAAI5U,KAAMsb,GACtC,IAAI2D,gBACFxE,EAAe,IAAIkF,aAAanF,GAEhCE,EAAS,IAAIpV,OAAOob,GAI1BhG,EAAO1V,SAAWmV,EAAiB5V,MAEnC,MAAMoc,EAAa,IAAIvF,WACrBtK,EAEA8P,EACAtF,EACAhQ,EACAkB,EACAiC,GAEIoS,EAAiB,IAAIhE,eAAe8D,EAAYnG,GAChDsG,EAAgB,IAAIxG,cACxBuG,EACArG,EACAC,EACAC,GAGIqG,EAAuB,IAAIC,aAC/BpM,EACAkM,EACArG,EACAD,EACAE,GAOF,OAFArC,kBAAkB0I,GAEXA,CACR,GAzEE,UAACxd,sBAAqB,IAGzBiS,EAAgBkL,EAAa1V,GAE7BwK,EAAgBkL,EAAa1V,EAAS,UAqExC,CClFAuV", "preExistingComment": "firebase-remote-config.js.map"}