import Link from 'next/link'

export function Footer() {
  return (
    <footer className="bg-navy-900 text-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          {/* Logo and Description */}
          <div className="col-span-1 md:col-span-2">
            <Link href="/" className="flex items-center space-x-2 mb-4">
              <div className="w-8 h-8 bg-gradient-to-br from-navy-600 to-teal-500 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-sm">uT</span>
              </div>
              <span className="text-xl font-bold">uTulz</span>
            </Link>
            <p className="text-slate-300 mb-4 max-w-md">
              Plataforma SaaS que ajuda agências e profissionais de publicidade 
              nas tarefas críticas do dia a dia com agentes de IA especializados.
            </p>
          </div>

          {/* Product Links */}
          <div>
            <h3 className="font-semibold mb-4">Produto</h3>
            <ul className="space-y-2">
              <li>
                <Link href="/ferramentas" className="text-slate-300 hover:text-white transition-colors">
                  Ferramentas
                </Link>
              </li>
              <li>
                <Link href="/preco" className="text-slate-300 hover:text-white transition-colors">
                  Preços
                </Link>
              </li>
              <li>
                <Link href="/campanha" className="text-slate-300 hover:text-white transition-colors">
                  Campanhas
                </Link>
              </li>
            </ul>
          </div>

          {/* Company Links */}
          <div>
            <h3 className="font-semibold mb-4">Empresa</h3>
            <ul className="space-y-2">
              <li>
                <Link href="/sobre" className="text-slate-300 hover:text-white transition-colors">
                  Sobre
                </Link>
              </li>
              <li>
                <Link href="/contato" className="text-slate-300 hover:text-white transition-colors">
                  Contato
                </Link>
              </li>
              <li>
                <Link href="/suporte" className="text-slate-300 hover:text-white transition-colors">
                  Suporte
                </Link>
              </li>
            </ul>
          </div>
        </div>

        <div className="border-t border-slate-700 mt-8 pt-8 flex flex-col md:flex-row justify-between items-center">
          <p className="text-slate-400 text-sm">
            © 2024 uTulz. Todos os direitos reservados.
          </p>
          <div className="flex space-x-6 mt-4 md:mt-0">
            <Link href="/privacidade" className="text-slate-400 hover:text-white text-sm transition-colors">
              Privacidade
            </Link>
            <Link href="/termos" className="text-slate-400 hover:text-white text-sm transition-colors">
              Termos
            </Link>
          </div>
        </div>
      </div>
    </footer>
  )
}
