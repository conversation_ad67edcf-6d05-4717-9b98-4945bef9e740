'use client'

import Link from 'next/link'
import { Button } from '@/components/ui/button'
import { Menu, X } from 'lucide-react'
import { useState } from 'react'

export function Header() {
  const [isMenuOpen, setIsMenuOpen] = useState(false)

  return (
    <header className="bg-white border-b border-slate-200 sticky top-0 z-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* Logo */}
          <Link href="/" className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-gradient-to-br from-navy-800 to-teal-600 rounded-lg flex items-center justify-center">
              <span className="text-white font-bold text-sm">uT</span>
            </div>
            <span className="text-xl font-bold text-navy-900">uTulz</span>
          </Link>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center space-x-8">
            <Link href="/ferramentas" className="text-slate-600 hover:text-navy-900 transition-colors">
              Ferramentas
            </Link>
            <Link href="/preco" className="text-slate-600 hover:text-navy-900 transition-colors">
              Preços
            </Link>
            <Link href="/campanha" className="text-slate-600 hover:text-navy-900 transition-colors">
              Campanhas
            </Link>
          </nav>

          {/* Desktop Auth Buttons */}
          <div className="hidden md:flex items-center space-x-4">
            <Link href="/login">
              <Button variant="ghost">Entrar</Button>
            </Link>
            <Link href="/registro">
              <Button variant="primary">Começar grátis</Button>
            </Link>
          </div>

          {/* Mobile Menu Button */}
          <button
            className="md:hidden p-2"
            onClick={() => setIsMenuOpen(!isMenuOpen)}
          >
            {isMenuOpen ? <X size={24} /> : <Menu size={24} />}
          </button>
        </div>

        {/* Mobile Menu */}
        {isMenuOpen && (
          <div className="md:hidden py-4 border-t border-slate-200">
            <nav className="flex flex-col space-y-4">
              <Link href="/ferramentas" className="text-slate-600 hover:text-navy-900 transition-colors">
                Ferramentas
              </Link>
              <Link href="/preco" className="text-slate-600 hover:text-navy-900 transition-colors">
                Preços
              </Link>
              <Link href="/campanha" className="text-slate-600 hover:text-navy-900 transition-colors">
                Campanhas
              </Link>
              <div className="flex flex-col space-y-2 pt-4">
                <Link href="/login">
                  <Button variant="ghost" className="w-full">Entrar</Button>
                </Link>
                <Link href="/registro">
                  <Button variant="primary" className="w-full">Começar grátis</Button>
                </Link>
              </div>
            </nav>
          </div>
        )}
      </div>
    </header>
  )
}
