import { Campaign, KPI } from '@/types/tools'

export const campaigns: Campaign[] = [
  {
    id: '1',
    name: 'Lançamento Produto X',
    client: 'TechCorp',
    status: 'Ativa',
    createdAt: new Date('2024-01-15'),
    updatedAt: new Date('2024-02-01'),
    toolsUsed: ['briefing-inicial', 'plano-midia', 'conceito-criativo-kv'],
    progress: 75,
  },
  {
    id: '2',
    name: 'Black Friday 2024',
    client: 'E-commerce Plus',
    status: 'Planejamento',
    createdAt: new Date('2024-01-20'),
    updatedAt: new Date('2024-01-28'),
    toolsUsed: ['briefing-inicial', 'metas-kpis'],
    progress: 25,
  },
  {
    id: '3',
    name: 'Rebranding Institucional',
    client: 'Construtora ABC',
    status: 'Ativa',
    createdAt: new Date('2024-01-10'),
    updatedAt: new Date('2024-02-02'),
    toolsUsed: ['briefing-inicial', 'conceito-criativo-kv', 'producao-peca-visual'],
    progress: 60,
  },
  {
    id: '4',
    name: '<PERSON><PERSON><PERSON>',
    client: 'Moda & Estilo',
    status: 'Finalizada',
    createdAt: new Date('2023-12-01'),
    updatedAt: new Date('2024-01-30'),
    toolsUsed: ['briefing-inicial', 'plano-midia', 'calendario-editorial', 'relatorio-final'],
    progress: 100,
  },
  {
    id: '5',
    name: 'Lançamento App Mobile',
    client: 'StartupTech',
    status: 'Ativa',
    createdAt: new Date('2024-01-25'),
    updatedAt: new Date('2024-02-01'),
    toolsUsed: ['briefing-inicial', 'setup-tracking'],
    progress: 40,
  },
  {
    id: '6',
    name: 'Dia das Mães 2024',
    client: 'Joalheria Premium',
    status: 'Pausada',
    createdAt: new Date('2024-01-18'),
    updatedAt: new Date('2024-01-25'),
    toolsUsed: ['briefing-inicial'],
    progress: 15,
  },
  {
    id: '7',
    name: 'Expansão Regional',
    client: 'Rede de Restaurantes',
    status: 'Ativa',
    createdAt: new Date('2024-01-12'),
    updatedAt: new Date('2024-02-02'),
    toolsUsed: ['briefing-inicial', 'plano-midia', 'publicacao-acompanhamento'],
    progress: 85,
  },
  {
    id: '8',
    name: 'Festival de Música',
    client: 'Produtora Cultural',
    status: 'Planejamento',
    createdAt: new Date('2024-01-30'),
    updatedAt: new Date('2024-02-01'),
    toolsUsed: ['briefing-inicial'],
    progress: 10,
  },
  {
    id: '9',
    name: 'Sustentabilidade Corporativa',
    client: 'Indústria Verde',
    status: 'Ativa',
    createdAt: new Date('2024-01-08'),
    updatedAt: new Date('2024-01-31'),
    toolsUsed: ['briefing-inicial', 'conceito-criativo-kv', 'criador-apresentacoes'],
    progress: 55,
  },
  {
    id: '10',
    name: 'Natal Premium 2023',
    client: 'Shopping Center',
    status: 'Finalizada',
    createdAt: new Date('2023-10-15'),
    updatedAt: new Date('2023-12-31'),
    toolsUsed: ['briefing-inicial', 'plano-midia', 'calendario-editorial', 'producao-peca-visual', 'relatorio-final'],
    progress: 100,
  },
]

export const kpis: KPI[] = [
  {
    label: 'Campanhas Ativas',
    value: 5,
    description: 'Campanhas em andamento',
    trend: 'up',
  },
  {
    label: 'Agentes Rodados',
    value: 127,
    description: 'Total de execuções este mês',
    trend: 'up',
  },
  {
    label: 'Última Atualização',
    value: 'Há 2 horas',
    description: 'Última atividade registrada',
    trend: 'stable',
  },
]
