export type ToolType = 'Docs' | 'Sheets' | 'Slides' | 'Imagem' | 'PDF'

export type ToolArea = 
  | 'Atendimento'
  | 'Planejamento' 
  | 'Mídia'
  | 'Criação'
  | 'Produção'
  | 'Publicação'
  | 'Social Media'
  | 'BI/Monitoramento'
  | 'Extras'
  | 'Conteúdo e Apresentação'

export interface Tool {
  id: string
  name: string
  description: string
  type: ToolType
  area: ToolArea
  isMvp: boolean
  isEnabled: boolean
}

export interface Campaign {
  id: string
  name: string
  client: string
  status: 'Ativa' | 'Pausada' | 'Finalizada' | 'Planejamento'
  createdAt: Date
  updatedAt: Date
  toolsUsed: string[]
  progress: number
}

export interface KPI {
  label: string
  value: string | number
  description?: string
  trend?: 'up' | 'down' | 'stable'
}
