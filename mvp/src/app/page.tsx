import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { ArrowRight, BarChart3, Target, Users, Zap } from 'lucide-react'
import Link from 'next/link'

export default function Home() {
  return (
    <div className="min-h-screen bg-gradient-to-b from-slate-50 to-white">
      {/* Hero Section */}
      <section className="pt-20 pb-16 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto text-center">
          <h1 className="text-4xl md:text-6xl font-bold text-navy-900 mb-6">
            Agentes de IA para
            <span className="text-teal-600 block">agências de publicidade</span>
          </h1>
          <p className="text-xl text-slate-600 mb-8 max-w-3xl mx-auto">
            Automatize tarefas críticas do dia a dia com ferramentas especializadas
            em atendimento, mídia, criação, produção e muito mais.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link href="/registro">
              <Button size="lg" variant="primary" className="text-lg px-8 py-4">
                Começar grátis
                <ArrowRight className="ml-2 h-5 w-5" />
              </Button>
            </Link>
            <Link href="/ferramentas">
              <Button size="lg" variant="outline" className="text-lg px-8 py-4">
                Ver ferramentas
              </Button>
            </Link>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-16 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-navy-900 mb-4">
              Tudo que sua agência precisa
            </h2>
            <p className="text-xl text-slate-600 max-w-2xl mx-auto">
              Ferramentas organizadas por área para otimizar cada etapa do seu processo criativo
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <Card className="text-center hover:shadow-lg transition-shadow">
              <CardHeader>
                <div className="w-12 h-12 bg-teal-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <Users className="h-6 w-6 text-teal-600" />
                </div>
                <CardTitle className="text-lg">Atendimento</CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription>
                  Briefings estruturados, status reports e gestão de aprovações
                </CardDescription>
              </CardContent>
            </Card>

            <Card className="text-center hover:shadow-lg transition-shadow">
              <CardHeader>
                <div className="w-12 h-12 bg-navy-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <Target className="h-6 w-6 text-navy-600" />
                </div>
                <CardTitle className="text-lg">Mídia</CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription>
                  Planejamento de canais, monitoramento e relatórios de performance
                </CardDescription>
              </CardContent>
            </Card>

            <Card className="text-center hover:shadow-lg transition-shadow">
              <CardHeader>
                <div className="w-12 h-12 bg-teal-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <Zap className="h-6 w-6 text-teal-600" />
                </div>
                <CardTitle className="text-lg">Criação</CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription>
                  Conceitos criativos, key visuals e direcionamentos de produção
                </CardDescription>
              </CardContent>
            </Card>

            <Card className="text-center hover:shadow-lg transition-shadow">
              <CardHeader>
                <div className="w-12 h-12 bg-navy-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <BarChart3 className="h-6 w-6 text-navy-600" />
                </div>
                <CardTitle className="text-lg">BI & Analytics</CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription>
                  Setup de tracking, dashboards e relatórios consolidados
                </CardDescription>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 px-4 sm:px-6 lg:px-8 bg-navy-900">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
            Pronto para transformar sua agência?
          </h2>
          <p className="text-xl text-slate-300 mb-8">
            Comece gratuitamente e veja como nossos agentes podem otimizar seu workflow
          </p>
          <Link href="/registro">
            <Button size="lg" variant="primary" className="text-lg px-8 py-4">
              Começar agora
              <ArrowRight className="ml-2 h-5 w-5" />
            </Button>
          </Link>
        </div>
      </section>
    </div>
  )
}
